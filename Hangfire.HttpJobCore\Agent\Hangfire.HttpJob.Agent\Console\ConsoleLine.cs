﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Threading;
using Newtonsoft.Json;

namespace Hangfire.HttpJob.Agent
{
    internal class ConsoleLine
    {
        
        
        
        [JsonProperty("t", Required = Required.Always)]
        public double TimeOffset { get; set; }

        
        
        
        [JsonProperty("r", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public bool IsReference { get; set; }

        
        
        
        [JsonProperty("s", Required = Required.Always)]
        public string Message { get; set; }

        
        
        
        [JsonProperty("c", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string TextColor { get; set; }

        
        
        
        [JsonProperty("p", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public double? ProgressValue { get; set; }

        
        
        
        [JsonProperty("n", DefaultValueHandling = DefaultValueHandling.Ignore)]
        public string ProgressName { get; set; }
    }
}
