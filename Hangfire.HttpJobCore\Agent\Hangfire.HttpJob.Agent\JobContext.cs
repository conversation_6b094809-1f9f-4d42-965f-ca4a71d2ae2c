﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Threading;
using Newtonsoft.Json;

namespace Hangfire.HttpJob.Agent
{
    public class JobContext:IDisposable
    {
        private Stopwatch _stopwatch;

        public JobContext()
        {
            isDispose = false;
            StartWatch();
        }
        public JobContext(CancellationTokenSource cancelToken)
        {
            isDispose = false;
            CancelToken = cancelToken;
            StartWatch();
        }

        #region Stopwatch



        private void StartWatch()
        {
            _stopwatch = Stopwatch.StartNew();
        }

        internal long GetElapsedMilliseconds()
        {
            if (_stopwatch == null) return -1;
            lock (_stopwatch)
            {
                if (_stopwatch == null) return -1;
                _stopwatch.Stop();
                var result = (long)_stopwatch.ElapsedMilliseconds;
                _stopwatch = null;
                return result;
            }
        }

        #endregion


        public string Param { get; set; }
        internal string RunJobId { get; set; }
        internal string HangfireServerId { get; set; }
        internal string ActionType { get; set; }
        internal volatile bool isDispose = false;

        public CancellationTokenSource CancelToken { get; internal set; }

        public IHangfireConsole Console { get; set; }

        
        
        
        internal Lazy<Dictionary<string,string>> LazyParseParam => new Lazy<Dictionary<string, string>>(() =>
        {
            if(string.IsNullOrEmpty(this.Param)) return null;
            try
            {
                Dictionary<string, string> datas = JsonConvert.DeserializeObject<Dictionary<string, string>>(this.Param);

                return datas;
            }
            catch (Exception)
            {
                //ignore
                return null;
            }
        });


        public JobItem JobItem { get; internal set; }
        public ConcurrentDictionary<string, string> Headers { get; set; }

        internal IHangfireStorage HangfireStorage { get; set; }

        
        
        
        
        
        
        
        public T GetParameter<T>(string key, T defaultValue)
        {
            try
            {
                Dictionary<string, string> datas = LazyParseParam.Value;

                if (datas != null)
                {
                    if (!datas.TryGetValue(key, out var stringValue))
                    {
                        return defaultValue;
                    }

                    return (T)Convert.ChangeType(stringValue, typeof(T));
                }
            }
            catch (Exception)
            {
                //ignore
            }

            return defaultValue;
        }

        public T GetParameter<T>(string key)
        {
            return GetParameter<T>(key, default);
        }

        
        
        
        
        
        public string GetHeader(string key)
        {
            if (this.Headers == null) return null;
            this.Headers.TryGetValue(key, out var value);
            return value;
        }

        public void Dispose()
        {
            Headers.Clear();
            CancelToken?.Dispose();
        }
    }

    public class JobItem
    {

        
        
        
        public string Url { get; set; }

        
        
        
        public string Method { get; set; }

        
        
        
        public string Data { get; set; }
        public string JobParam { get; set; }

        
        
        
        public string ContentType { get; set; }

        
        
        
        public int Timeout { get; set; }

        
        
        
        public int DelayFromMinutes { get; set; }
        
        
        
        public string Cron { get; set; }
        
        
        
        public string JobName { get; set; }

        
        
        
        public string JobDetailUrl { get; set; }

        
        
        
        public string RecurringJobIdentifier { get; set; }

        
        
        
        public string JobId { get; set; }

        
        
        
        public string HangfireServerId { get; set; }

        
        
        
        public string QueueName { get; set; }

        
        
        
        public string AgentClass { get; set; }

        
        
        
        public int AgentTimeout { get; set; }

        
        
        
        public bool SendSuccess { get; set; }

        
        
        
        public bool SendFail { get; set; }

        
        
        
        public string Mail { get; set; }

        
        
        
        public bool EnableRetry { get; set; }

        
        
        
        public string RetryDelaysInSeconds { get; set; }

        
        
        
        public int RetryTimes { get; set; }

        
        
        
        public string BasicUserName { get; set; }
        
        
        
        public string BasicPassword { get; set; }

        
        
        
        public Dictionary<string, string> Headers { get; set; }

        
        
        
        public string CallbackEL { get; set; }

        
        
        
        public string TimeZone { get; set; }

        
        
        
        public DingTalkOption DingTalk { get; set; }

        
        
        
        internal JobStorageConfig Storage { get; set; }
    }
    public class DingTalkOption
    {
        
        
        
        public string Token { get; set; }

        
        
        
        public string AtPhones { get; set; }

        
        ///  通知是否@所有人
        
        public bool IsAtAll { get; set; }
    }

    public class JobStorageConfig
    {
        public static JobStorageConfig LocalJobStorageConfig;

        public string Type { get; set; }
        public string TablePrefix { get; set; }
        public string HangfireDb { get; set; }
        public int? ExpireAtDays { get; set; }
        public TimeSpan? ExpireAt { get; set; }
        public int? Db { get; set; }

        public override bool Equals(object obj)
        {
            var item = obj as JobStorageConfig;

            if (item == null)
            {
                return false;
            }

            return this.Type.Equals(item.Type) && this.TablePrefix.Equals(item.TablePrefix) && this.HangfireDb.Equals(item.HangfireDb) && this.Db.Equals(item.Db);
        }

        public override int GetHashCode()
        {
            unchecked
            {
                return ((this.HangfireDb != null ? this.HangfireDb.GetHashCode() : 0)) ^ (this.TablePrefix != null ? this.TablePrefix.GetHashCode() : 0);
            }
        }
    }
}
