﻿using System;
using System.Collections.Generic;

namespace Hangfire.HttpJob.Client
{


    
    
    
    public class HttpCallbackJob
    {
        public HttpCallbackJob()
        {
            Method = "Post";
            ContentType = "application/json";
            Timeout = 20000;
        }

        
        
        
        public string JobKeyWords { get; set; }

        
        
        
        public string Url { get; set; }

        
        
        
        public string Method { get; set; } 
        
        
        
        
        public object Data { get; set; }

        
        
        
        public string ContentType { get; set; }

        
        
        
        public int Timeout { get; set; }
        
        
        
        
        public string BasicUserName { get; set; }

        
        
        
        public string BasicPassword { get; set; }

        
        
        
        public string AgentClass { get; set; }

        
        
        
        public string CallbackEL { get; set; }

        
        
        
        public Dictionary<string,string> Headers { get; set; } =new Dictionary<string, string>();
        
        public HttpCallbackJob Success { get; set; }
        
        public HttpCallbackJob Fail { get; set; }
    }
    
    
    
    
    public class BackgroundJob
    {
        public BackgroundJob()
        {
            Method = "Post";
            ContentType = "application/json";
            Timeout = 20000;
            DelayFromMinutes = 15;
            SendFail = true;
        }

        
        
        
        public string JobKeyWords { get; set; }

        
        
        
        public string Url { get; set; }

        
        
        
        public string Method { get; set; }


        
        
        
        public object Data { get; set; }

        
        
        
        public string ContentType { get; set; }

        
        
        
        public int Timeout { get; set; }

        
        
        
        public int DelayFromMinutes { get; set; }

        public DateTime? RunAt { get; set; }

        
        
        
        public string JobName { get; set; }

        
        
        
        public bool SendSuccess { get; set; }

        
        
        
        public bool SendFail { get; set; }

        
        
        
        public List<string> Mail { get; set; }

        
        
        
        public bool EnableRetry { get; set; }

        
        
        
        public int RetryTimes { get; set; }

        
        
        
        public string RetryDelaysInSeconds { get; set; }

        
        
        
        public string BasicUserName { get; set; }

        
        
        
        public string BasicPassword { get; set; }

        
        
        
        public string AgentClass { get; set; }

        
        
        
        public string CallbackEL { get; set; }

        
        
        
        public string QueueName { get; set; }


        
        
        
        public Dictionary<string,string> Headers { get; set; } =new Dictionary<string, string>();

        
        
        
        public Dictionary<string, object> QueryParams { get; set; } = new Dictionary<string, object>();

        public HttpCallbackJob Success { get; set; }
        public HttpCallbackJob Fail { get; set; }
        
        
        
        public string TimeZone { get; set; }

        
        
        
        public DingTalkOption DingTalk { get; set; }

    }
}
