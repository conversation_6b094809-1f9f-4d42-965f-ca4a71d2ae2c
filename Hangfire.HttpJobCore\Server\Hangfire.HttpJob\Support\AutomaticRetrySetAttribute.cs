﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using Hangfire.Common;
using Hangfire.HttpJob.Server;
using Hangfire.Logging;
using Hangfire.States;
using Hangfire.Storage;
using Newtonsoft.Json;

namespace Hangfire.HttpJob.Support
{
    internal class AutomaticRetrySetAttribute : JobFilterAttribute, IElectStateFilter, IApplyStateFilter
    {
        
        
        
        
        
        
        public static readonly int DefaultRetryAttempts = 5;

        private static readonly Func<long, int> DefaultDelayInSecondsByAttemptFunc = attempt =>
        {
            var random = new Random();
            return (int)Math.Round(
                Math.Pow(attempt - 1, 4) + 15 + random.Next(30) * attempt);
        };

        private readonly ILog _logger = LogProvider.For<AutomaticRetrySetAttribute>();

        private readonly object _lockObject = new object();
        private int _attempts;
        private int[] _delaysInSeconds;
        private Func<long, int> _delayInSecondsByAttemptFunc;
        private AttemptsExceededAction _onAttemptsExceeded;
        private bool _logEvents;

        
        
        
        
        public AutomaticRetrySetAttribute()
        {
            //Attempts = DefaultRetryAttempts;
            //DelayInSecondsByAttemptFunc = DefaultDelayInSecondsByAttemptFunc;
            //LogEvents = true;
            //OnAttemptsExceeded = AttemptsExceededAction.Fail;
            //Order = 20;
        }

        
        
        
        
        
        public int Attempts
        {
            get { lock (_lockObject) { return _attempts; } }
            set
            {
                if (value < 0)
                {
                    throw new ArgumentOutOfRangeException(nameof(value), @"Attempts value must be equal or greater than zero.");
                }

                lock (_lockObject)
                {
                    _attempts = value;
                }
            }
        }

        
        
        
        
        
        
        public int[] DelaysInSeconds
        {
            get { lock (_lockObject) { return _delaysInSeconds; } }
            set
            {
                if (value == null || value.Length == 0) throw new ArgumentNullException(nameof(value));
                if (value.Any(delay => delay < 0)) throw new ArgumentException($@"{nameof(DelaysInSeconds)} value must be an array of non-negative numbers.", nameof(value));

                lock (_lockObject) { _delaysInSeconds = value; }
            }
        }

        
        
        
        
        public Func<long, int> DelayInSecondsByAttemptFunc
        {
            get { lock (_lockObject) { return _delayInSecondsByAttemptFunc; } }
            set
            {
                if (value == null) throw new ArgumentNullException(nameof(value));
                lock (_lockObject) { _delayInSecondsByAttemptFunc = value; }
            }
        }

        
        
        
        
        public AttemptsExceededAction OnAttemptsExceeded
        {
            get { lock (_lockObject) { return _onAttemptsExceeded; } }
            set { lock (_lockObject) { _onAttemptsExceeded = value; } }
        }

        
        
        
        public bool LogEvents
        {
            get { lock (_lockObject) { return _logEvents; } }
            set { lock (_lockObject) { _logEvents = value; } }
        }

        
        public void OnStateElection(ElectStateContext context)
        {
            var jobdta = context.BackgroundJob.Job.Args.FirstOrDefault();
            if (!(jobdta is HttpJobItem httpjob) )
            {
                return;
            }

            if (!httpjob.EnableRetry) return;

            //如果先执行失败的话 就直接失败
            var failedState = context.CandidateState as FailedState;
            if (failedState == null)
            {
                
                return;
            }

            var retryTimesLimit = Attempts;
            var retryAttempt = context.GetJobParameter<int>("RetryCount") + 1;
            if (httpjob.RetryTimes > 0)
            {
                //自定义设置了超时配置
                retryTimesLimit = httpjob.RetryTimes;
            }
            if (retryAttempt <= retryTimesLimit)
            {
                ScheduleAgainLater(context, retryAttempt, retryTimesLimit,failedState);
                return;
            }

            try
            {
                var isCronJob = !string.IsNullOrEmpty(httpjob.Cron);
                var jobKey = isCronJob ? ((!string.IsNullOrEmpty(httpjob.RecurringJobIdentifier)?httpjob.RecurringJobIdentifier:httpjob.JobName)) : context.BackgroundJob.Id;

                //删除Runtime数据 代表的是需要重试 但是已经重试到最大次数了
                var hashKey = CodingUtil.MD5(jobKey + ".runtime");
                context.Transaction.RemoveHash(hashKey);
            }
            catch (Exception)
            {
                //ignore
            }
            
            if (retryAttempt > retryTimesLimit && OnAttemptsExceeded == AttemptsExceededAction.Delete)
            {
                TransitionToDeleted(context, failedState);
            }
            else
            {
                if (LogEvents)
                {
                    _logger.ErrorException(
                        $"Failed to process the job '{context.BackgroundJob.Id}': an exception occurred.",
                        failedState.Exception);
                }
            }
        }

        
        
        public void OnStateApplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
        {
            var httpjob = context.BackgroundJob.Job.Args.FirstOrDefault() as HttpJobItem;
            if (httpjob == null || !httpjob.EnableRetry) { return; }
            if (context.NewState is ScheduledState &&
                context.NewState.Reason != null &&
                context.NewState.Reason.StartsWith("Retry attempt"))
            {
                transaction.AddToSet("retries", context.BackgroundJob.Id);
            }
        }

        
        public void OnStateUnapplied(ApplyStateContext context, IWriteOnlyTransaction transaction)
        {
            if (context.OldStateName == ScheduledState.StateName)
            {
                transaction.RemoveFromSet("retries", context.BackgroundJob.Id);
            }
        }

        
        
        
        
        
        
        
        private void ScheduleAgainLater(ElectStateContext context, int retryAttempt,int totalLimit, FailedState failedState)
        {
            var httpjob = context.BackgroundJob.Job.Args.FirstOrDefault() as HttpJobItem;
            if (httpjob ==null || !httpjob.EnableRetry) { return; }
            context.SetJobParameter("RetryCount", retryAttempt);
            int delayInSeconds;

            var delayArr = _delaysInSeconds;
            if (!string.IsNullOrEmpty(httpjob.RetryDelaysInSeconds))
            {
                var delayArrStr = httpjob.RetryDelaysInSeconds.Replace("，","").Split(new string[] { ","},StringSplitOptions.RemoveEmptyEntries);
                if (delayArrStr.Any())
                {
                    try
                    {
                        var tempArr = delayArrStr.Select(int.Parse).ToArray();
                        if (tempArr.Any()) delayArr = tempArr;
                    }
                    catch (Exception)
                    {
                       //ignore
                    }
                   
                }
            }

            if (delayArr != null)
            {
                delayInSeconds = retryAttempt <= delayArr.Length
                    ? delayArr[retryAttempt - 1]
                    : delayArr.Last();
            }
            else
            {
                delayInSeconds = _delayInSecondsByAttemptFunc(retryAttempt);
            }

            var delay = TimeSpan.FromSeconds(delayInSeconds);

            const int maxMessageLength = 50;
            var exceptionMessage = failedState.Exception.Message.Length > maxMessageLength
                ? failedState.Exception.Message.Substring(0, maxMessageLength - 1) + "…"
                : failedState.Exception.Message;

            
            

            var reason = $"Delay {delayInSeconds}s Retry attempt {retryAttempt} of {totalLimit}: {exceptionMessage}";

            context.CandidateState = delay == TimeSpan.Zero
                ? (IState)new EnqueuedState { Reason = reason }
                : new ScheduledState(delay) { Reason = reason };

            if (LogEvents)
            {
                _logger.WarnException(
                    $"Failed to process the job '{context.BackgroundJob.Id}': an exception occurred. Retry attempt {retryAttempt} of {totalLimit} will be performed in {delayInSeconds}s.",
                    failedState.Exception);
            }
        }

        
        
        
        
        
        private void TransitionToDeleted(ElectStateContext context, FailedState failedState)
        {
            context.CandidateState = new DeletedState
            {
                Reason = Attempts > 0
                    ? "Exceeded the maximum number of retry attempts."
                    : "Retries were disabled for this job."
            };

            if (LogEvents)
            {
                _logger.WarnException(
                    $"Failed to process the job '{context.BackgroundJob.Id}': an exception occured. Job was automatically deleted because the retry attempt count exceeded {Attempts}.",
                    failedState.Exception);
            }
        }
    }
}
