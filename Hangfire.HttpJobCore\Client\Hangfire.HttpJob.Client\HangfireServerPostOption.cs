﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Hangfire.HttpJob.Client
{

    
    
    
    public class HangfireServerPostOption
    {
        public HangfireServerPostOption()
        {
            TimeOut = 5000;
            ThrowException = false;
        }

        
        
        
        public int TimeOut { get; set; }

        
        
        
        public bool ThrowException { get; set; }

        
        
        
        public string BasicUserName { get; set; }

        
        
        
        public string BasicPassword { get; set; }

        
        
        
        public string ProxyUrl { get; set; }

        internal HttpClient HttpClient { get; set; }
    }
}
