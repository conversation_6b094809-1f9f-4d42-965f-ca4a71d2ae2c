<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="hangfire\hangfire_global.json" />
    <Content Remove="NLog.Config" />
  </ItemGroup>

  <ItemGroup>
    <None Include="hangfire\hangfire_global.json" />
    <None Include="NLog.Config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire.Dashboard.BasicAuthorization" Version="1.0.2" />
    <PackageReference Include="nacos-sdk-csharp-unofficial.AspNetCore" Version="0.8.5" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="4.14.0" />
    <PackageReference Include="RabbitMQ.Client" Version="6.4.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Hangfire.MySql\Hangfire.MySql.csproj" />
    <ProjectReference Include="..\..\Server\Hangfire.HttpJob\Hangfire.HttpJob.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Dockerfile">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
