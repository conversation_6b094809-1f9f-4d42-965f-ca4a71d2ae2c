using Hangfire;
using Hangfire.Console;
using Hangfire.Dashboard.BasicAuthorization;
using Hangfire.HttpJob;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using Hangfire.Heartbeat;
using Hangfire.Heartbeat.Server;
using Hangfire.MySql;
using Hangfire.Server;
using Microsoft.AspNetCore.Localization;
using Newtonsoft.Json;
using Spring.Core.TypeConversion;
using IsolationLevel = System.Transactions.IsolationLevel;
using Newtonsoft.Json.Linq;
using Hangfire.HttpJob.Server;
using Hangfire.HttpJob.Support;
using MySqlConnector;

namespace MysqlHangfire
{
    public static class HangfireCollectionExtensions
    {
        private static string sqlConnectStr = "";

        public static IServiceCollection AddSelfHangfire(this IServiceCollection services, JObject appConfigDto)
        {
            services.AddTransient<IBackgroundProcess, ProcessMonitor>();

            services.AddHangfire(globalConfiguration =>
            {
                services.ConfigurationHangfire(appConfigDto, globalConfiguration);
            });



            services.AddHangfireServer((provider, config) =>
            {
                var settings = JsonConvert.DeserializeObject<HangfireSettings>(appConfigDto["Hangfire"]["HangfireSettings"].ToString());
                ConfigFromEnv(settings);
                var queues = settings.JobQueues.Select(m => m.ToLower()).Distinct().ToList();
                var workerCount = Math.Max(Environment.ProcessorCount, settings.WorkerCount); //工作线程数，当前允许的最大线程，默认20


                config.ServerName = settings.ServerName;
                config.ServerTimeout = TimeSpan.FromMinutes(4);
                config.SchedulePollingInterval = TimeSpan.FromSeconds(3);//秒级任务需要配置短点，一般任务可以配置默认时间，默认15秒
                config.ShutdownTimeout = TimeSpan.FromMinutes(30); //超时时间
                config.Queues = queues.ToArray(); //队列
                config.WorkerCount = workerCount;
            });

            return services;
        }


        public static void ConfigurationHangfire(this IServiceCollection services, JObject appConfigDto,
            IGlobalConfiguration globalConfiguration)
        {
            var serverProvider = services.BuildServiceProvider();

            var langStr = appConfigDto["Hangfire"]["HttpJobOptions"]["Lang"].ToString();
            var envLangStr = GetEnvConfig<string>("Lang");
            if (!string.IsNullOrEmpty(envLangStr)) langStr = envLangStr;
            if (!string.IsNullOrEmpty(langStr))
            {
                System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo(langStr);
            }

            var hangfireSettings = JsonConvert.DeserializeObject<HangfireSettings>(appConfigDto["Hangfire"]["HangfireSettings"].ToString());
            ConfigFromEnv(hangfireSettings);

            var httpJobOptions = JsonConvert.DeserializeObject<HangfireHttpJobOptions>(appConfigDto["Hangfire"]["HttpJobOptions"].ToString());
            ConfigFromEnv(httpJobOptions);

            httpJobOptions.GlobalSettingJsonFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory!, "hangfire",
                "hangfire_global.json");

            sqlConnectStr = appConfigDto["Hangfire"]["HangfireSettings"]["ConnectionString"].ToString();
            var envSqlConnectStr = GetEnvConfig<string>("HangfireMysqlConnectionString");
            if (!string.IsNullOrEmpty(envSqlConnectStr)) sqlConnectStr = envSqlConnectStr;

            var mysqlOption = new MySqlStorageOptions
            {
                TransactionIsolationLevel = IsolationLevel.ReadCommitted,
                QueuePollInterval = TimeSpan.FromSeconds(15),
                JobExpirationCheckInterval = TimeSpan.FromHours(1),
                CountersAggregateInterval = TimeSpan.FromMinutes(5),
                PrepareSchemaIfNecessary = true,
                DashboardJobListLimit = 50000,
                TransactionTimeout = TimeSpan.FromMinutes(1),
                TablesPrefix = hangfireSettings.TablePrefix
            };

            globalConfiguration
                .UseStorage(new MySqlStorage(sqlConnectStr, mysqlOption))
                .UseConsole(new ConsoleOptions
                {
                    BackgroundColor = "#000079"
                })
                /*.UseTagsWithMySql(new TagsOptions()
                {
                    TagsListStyle = TagsListStyle.Dropdown
                }, sqlOptions: mysqlOption)*/
                .UseHangfireHttpJob(httpJobOptions, item => AddJobExtendInfo(item), (jobId, statueName, excuteResult) => UpdateJobExtendInfo(jobId, statueName, excuteResult))
                .UseHeartbeatPage();
        }

        public static IApplicationBuilder ConfigureSelfHangfire(this IApplicationBuilder app, JObject appConfigDto)
        {
            var langStr = appConfigDto["Hangfire"]["HttpJobOptions"]["Lang"].ToString();
            var envLangStr = GetEnvConfig<string>("Lang");
            if (!string.IsNullOrEmpty(envLangStr)) langStr = envLangStr;

            if (!string.IsNullOrEmpty(langStr))
            {
                var options = new RequestLocalizationOptions
                {
                    DefaultRequestCulture = new RequestCulture(langStr)
                };
                app.UseRequestLocalization(options);
            }

            var services = app.ApplicationServices;
            var hangfireSettings = JsonConvert.DeserializeObject<HangfireSettings>(appConfigDto["Hangfire"]["HangfireSettings"].ToString());


            var dashbordConfig = new DashboardOptions
            {
                AppPath = "#",
                IgnoreAntiforgeryToken = true,
                DisplayStorageConnectionString = hangfireSettings.DisplayStorageConnectionString,
                IsReadOnlyFunc = Context => false
            };

            if (hangfireSettings.HttpAuthInfo.IsOpenLogin && hangfireSettings.HttpAuthInfo.Users.Any())
            {
                var httpAuthInfo = hangfireSettings.HttpAuthInfo;
                var users = hangfireSettings.HttpAuthInfo.Users.Select(m => new BasicAuthAuthorizationUser
                {
                    Login = m.Login,
                    Password = m.Password,
                    PasswordClear = m.PasswordClear
                });

                var basicAuthAuthorizationFilterOptions = new BasicAuthAuthorizationFilterOptions
                {
                    RequireSsl = httpAuthInfo.RequireSsl,
                    SslRedirect = httpAuthInfo.SslRedirect,
                    LoginCaseSensitive = httpAuthInfo.LoginCaseSensitive,
                    Users = users
                };

                dashbordConfig.Authorization = new[]
                {
                    new BasicAuthAuthorizationFilter(basicAuthAuthorizationFilterOptions)
                };

            }

            app.UseHangfireDashboard(hangfireSettings.StartUpPath, dashbordConfig);

            if (!string.IsNullOrEmpty(hangfireSettings.ReadOnlyPath))
                //只读面板，只能读取不能操作 
                app.UseHangfireDashboard(hangfireSettings.ReadOnlyPath, new DashboardOptions
                {
                    IgnoreAntiforgeryToken = true,
                    AppPath = hangfireSettings.StartUpPath, //返回时跳转的地址
                    DisplayStorageConnectionString = false, //是否显示数据库连接信息
                    IsReadOnlyFunc = Context => true
                });

            
            CreateJobExtendTable();

            
            CodingUtil.GetApiRoutes();

            return app;
        }

        #region Docker运行的参数配置https://github.com/yuzd/Hangfire.HttpJob/wiki/000.Docker-Quick-Start


        private static void ConfigFromEnv(HangfireSettings settings)
        {
            var hangfireQueues = GetEnvConfig<string>("HangfireQueues");
            if (!string.IsNullOrEmpty(hangfireQueues))
            {
                settings.JobQueues = hangfireQueues.Split(',').ToList();
            }
            var serverName = GetEnvConfig<string>("ServerName");
            if (!string.IsNullOrEmpty(serverName))
            {
                settings.ServerName = serverName;
            }
            var workerCount = GetEnvConfig<string>("WorkerCount");
            if (!string.IsNullOrEmpty(workerCount))
            {
                settings.WorkerCount = int.Parse(workerCount);
            }

            var tablePrefix = GetEnvConfig<string>("TablePrefix");
            if (!string.IsNullOrEmpty(tablePrefix))
            {
                settings.TablePrefix = tablePrefix;
            }

            var hangfireUserName = GetEnvConfig<string>("HangfireUserName");
            var hangfirePwd = GetEnvConfig<string>("HangfirePwd");
            if (!string.IsNullOrEmpty(hangfireUserName) && !string.IsNullOrEmpty(hangfirePwd))
            {
                settings.HttpAuthInfo = new HttpAuthInfo { Users = new List<UserInfo>() };
                settings.HttpAuthInfo.Users.Add(new UserInfo
                {
                    Login = hangfireUserName,
                    PasswordClear = hangfirePwd
                });
            }
        }

        private static void ConfigFromEnv(HangfireHttpJobOptions settings)
        {
            var defaultRecurringQueueName = GetEnvConfig<string>("DefaultRecurringQueueName");
            if (!string.IsNullOrEmpty(defaultRecurringQueueName))
            {
                settings.DefaultRecurringQueueName = defaultRecurringQueueName;
            }

            if (settings.MailOption == null) settings.MailOption = new MailOption();

            var hangfireMailServer = GetEnvConfig<string>("HangfireMail_Server");
            if (!string.IsNullOrEmpty(hangfireMailServer))
            {
                settings.MailOption.Server = hangfireMailServer;
            }

            var hangfireMailPort = GetEnvConfig<int>("HangfireMail_Port");
            if (hangfireMailPort > 0)
            {
                settings.MailOption.Port = hangfireMailPort;
            }

            var hangfireMailUseSsl = Environment.GetEnvironmentVariable("HangfireMail_UseSsl");
            if (!string.IsNullOrEmpty(hangfireMailUseSsl))
            {
                settings.MailOption.UseSsl = hangfireMailUseSsl.ToLower().Equals("true");
            }

            var hangfireMailUser = GetEnvConfig<string>("HangfireMail_User");
            if (!string.IsNullOrEmpty(hangfireMailUser))
            {
                settings.MailOption.User = hangfireMailUser;
            }

            var hangfireMailPassword = GetEnvConfig<string>("HangfireMail_Password");
            if (!string.IsNullOrEmpty(hangfireMailPassword))
            {
                settings.MailOption.Password = hangfireMailPassword;
            }

        }
        private static T GetEnvConfig<T>(string key)
        {
            try
            {
                var value = Environment.GetEnvironmentVariable(key.Replace(":", "_"));
                if (!string.IsNullOrEmpty(value))
                {
                    return (T)TypeConversionUtils.ConvertValueIfNecessary(typeof(T), value, null);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return default;
        }

        #endregion

        private static void CreateJobExtendTable()
        {
            string sql = @" CREATE TABLE IF NOT EXISTS `hangfire.JobExtend` (
                              JobId bigint(20) NOT NULL,
                              QueueName varchar(200),
                              JobName varchar(200),
                              StateName nvarchar(20),
                              KeyWords nvarchar(2000),
                              Arguments text,
                              CreatedAt datetime(3),
                              FinishAt datetime(3),
                              ExcuteResult text,
                              PRIMARY KEY (JobId)
                            )
                            ENGINE = INNODB,
                            CHARACTER SET utf8mb4,
                            COLLATE utf8mb4_general_ci;";
            UseConnection(connection =>
            {
                var mysqlCommand = connection.CreateCommand();
                mysqlCommand.CommandType = CommandType.Text;
                mysqlCommand.CommandText = sql;
                mysqlCommand.ExecuteNonQuery();
            });
        }

        private static void AddJobExtendInfo(HttpJobItem jobItem)
        {
            string addSql = $"insert into `hangfire.JobExtend` (JobId, QueueName, JobName, StateName, KeyWords, Arguments, CreatedAt) values (?JobId, ?QueueName, ?JobName, ?StateName, ?KeyWords, ?Arguments, ?CreatedAt);";
            UseConnection(connection =>
            {
                var mysqlCommand = connection.CreateCommand();
                mysqlCommand.CommandType = CommandType.Text;
                mysqlCommand.CommandText = addSql;
                mysqlCommand.Parameters.Add(new MySqlParameter { ParameterName = "?JobId", MySqlDbType = MySqlDbType.Int64, Value = jobItem.JobId });
                mysqlCommand.Parameters.Add(new MySqlParameter { ParameterName = "?QueueName", MySqlDbType = MySqlDbType.VarChar, Value = jobItem.QueueName });
                mysqlCommand.Parameters.Add(new MySqlParameter { ParameterName = "?JobName", MySqlDbType = MySqlDbType.VarChar, Value = jobItem.JobName });
                mysqlCommand.Parameters.Add(new MySqlParameter { ParameterName = "?StateName", MySqlDbType = MySqlDbType.VarChar, Value = "Enqueued" });
                mysqlCommand.Parameters.Add(new MySqlParameter { ParameterName = "?KeyWords", MySqlDbType = MySqlDbType.VarChar, Value = jobItem.JobKeyWords });
                mysqlCommand.Parameters.Add(new MySqlParameter { ParameterName = "?Arguments", MySqlDbType = MySqlDbType.Text, Value = jobItem.ToString() });
                mysqlCommand.Parameters.Add(new MySqlParameter { ParameterName = "?CreatedAt", MySqlDbType = MySqlDbType.DateTime, Value = DateTime.UtcNow });
                mysqlCommand.ExecuteNonQuery();
            });
        }

        private static void UpdateJobExtendInfo(long jobId, string stateName, string excuteResult)
        {
            string updateSql = $" update `hangfire.JobExtend` set StateName=?StateName, ExcuteResult=?ExcuteResult, FinishAt=?FinishAt where JobId=?JobId";
            UseConnection(connection =>
            {
                var mysqlCommand = connection.CreateCommand();
                mysqlCommand.CommandType = CommandType.Text;
                mysqlCommand.CommandText = updateSql;
                mysqlCommand.Parameters.Add(new MySqlParameter { ParameterName = "?JobId", MySqlDbType = MySqlDbType.Int64, Value = jobId });
                mysqlCommand.Parameters.Add(new MySqlParameter { ParameterName = "?StateName", MySqlDbType = MySqlDbType.VarChar, Value = stateName });
                mysqlCommand.Parameters.Add(new MySqlParameter { ParameterName = "?ExcuteResult", MySqlDbType = MySqlDbType.Text, Value = excuteResult });
                mysqlCommand.Parameters.Add(new MySqlParameter { ParameterName = "?FinishAt", MySqlDbType = MySqlDbType.DateTime, Value = DateTime.UtcNow });
                mysqlCommand.ExecuteNonQuery();
            });
        }

        private static MySqlConnection CreateAndOpenConnection()
        {
            var connection = new MySqlConnection(sqlConnectStr);
            connection.Open();

            return connection;
        }

        private static void UseConnection(Action<MySqlConnection> func)
        {
            MySqlConnection connection = null;

            try
            {
                connection = CreateAndOpenConnection();
                func(connection);
            }
            finally
            {
                ReleaseConnection(connection);
            }
        }

        private static void ReleaseConnection(IDbConnection connection)
        {
            if (connection != null)
            {
                connection.Close();
                connection.Dispose();
            }
        }
    }

    public class HangfireSettings
    {
        public string ServerName { get; set; }
        public string TablePrefix { get; set; }
        public string StartUpPath { get; set; }
        public string ReadOnlyPath { get; set; }
        public List<string> JobQueues { get; set; }
        public HttpAuthInfo HttpAuthInfo { get; set; } = new HttpAuthInfo();
        public int WorkerCount { get; set; } = 40;
        public bool DisplayStorageConnectionString { get; set; } = false;
    }

    public class HttpAuthInfo
    {
        
        
        
        public bool SslRedirect { get; set; } = false;

        
        
        
        public bool RequireSsl { get; set; } = false;

        
        
        
        public bool LoginCaseSensitive { get; set; } = true;

        public bool IsOpenLogin { get; set; } = true;

        public List<UserInfo> Users { get; set; } = new List<UserInfo>();
    }

    public class UserInfo
    {
        public string Login { get; set; }
        public string PasswordClear { get; set; }

        public byte[] Password => Encoding.UTF8.GetBytes(PasswordClear);
    }
}