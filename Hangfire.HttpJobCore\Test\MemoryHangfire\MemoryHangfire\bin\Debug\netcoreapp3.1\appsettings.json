﻿{
  "Logging": {
    "IncludeScopes": false,
    "LogLevel": {
      "Default": "Trace",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "Hangfire": {
    "HangfireSettings": {
      "ServerName": "MemoryHangfire",
      "TablePrefix": "hangfire",
      "StartUpPath": "/job",
      "ReadOnlyPath": "",
      "JobQueues": [ "default", "apis", "recurring" ],
      "WorkerCount": 50,
      "DisplayStorageConnectionString": false,
      "HttpAuthInfo": {
        "SslRedirect": false,
        "RequireSsl": false,
        "LoginCaseSensitive": true,
        "IsOpenLogin": true,
        "Users": [
          {
            "Login": "admin",
            "PasswordClear": "test"
          }
        ]
      }
    },
    "HttpJobOptions": {
      "Lang": "zh",
      "DefaultTimeZone": "",
      "CurrentDomain": "//",
      "EnableDingTalk": true,
      "DefaultRecurringQueueName": "recurring",
      "GlobalSettingJsonFilePath": "",
      "Proxy": "",
      "JobExpirationTimeoutDay": 7,
      "GlobalHttpTimeOut": 5000,
      "MailOption": {
        "Server": "",
        "Port": 0,
        "User": "",
        "Password": "",
        "UseSsl": false,
        "AlertMailList": []
      },
      "DingTalkOption": {
        "Token": "",
        "AtPhones": "",
        "IsAtAll": false
      }
    }
  }
}
