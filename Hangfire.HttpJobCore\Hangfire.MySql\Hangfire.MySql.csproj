﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <Description>Hangfire MySql Storage</Description>
        <PackageId>Hangfire.MySqlStorage</PackageId>
        <Product>Hangfire MySql Storage</Product>
        <Company />
        <Authors><PERSON><PERSON></Authors>
        <PackageLicenseUrl>https://github.com/arnoldasgudas/Hangfire.MySqlStorage/blob/master/License.md</PackageLicenseUrl>
        <PackageProjectUrl>https://github.com/arnoldasgudas/Hangfire.MySqlStorage</PackageProjectUrl>
        <Copyright>Copyright 2015</Copyright>
        <PackageTags>Hangfire MySql Hangfire.MySql</PackageTags>
        <TargetFramework>netstandard2.0</TargetFramework>
    </PropertyGroup>
    <ItemGroup>
        <None Remove="Install.sql" />
    </ItemGroup>
    <ItemGroup>
        <EmbeddedResource Include="Install.sql" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.CSharp" Version="4.4.1" />
        <PackageReference Include="Newtonsoft.Json" Version="11.0.2" />
        <PackageReference Include="Dapper" Version="1.50.5" />
        <PackageReference Include="Hangfire.Core" Version="1.6.21" />
    </ItemGroup>
    <ItemGroup Condition="'$(TargetFramework)' == 'netstandard2.0'">
      <PackageReference Include="MySqlConnector" Version="2.3.0-beta.3" />
    </ItemGroup>
</Project>