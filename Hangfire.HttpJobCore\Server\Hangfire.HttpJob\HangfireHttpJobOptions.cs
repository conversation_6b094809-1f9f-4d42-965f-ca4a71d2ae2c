using System;
using System.Collections.Generic;
using System.Net;
using Hangfire.HttpJob.Content.resx;
using Hangfire.HttpJob.Server;
using Hangfire.States;
using HttpClientFactory;
using Newtonsoft.Json;

namespace Hangfire.HttpJob
{
    public class HangfireHttpJobOptions
    {
        
        
        
        
        public IHttpClientFactory HttpJobClientFactory{ get; set; }
        
        
        
        
        public IHttpClientFactory DingTalkClientFactory{ get; set; }
        
        
        
        
        public int GlobalHttpTimeOut { get; set; } = 5000;
        
        
        
        
        
        public int GlobalHttpClientTimeOut { get; set; }

        
        
        
        public string DefaultTimeZone { get; set; }

        
        
        
        public long JobExpirationTimeoutDay { get; set; } = 7;
   
        
        
        
        public string DefaultRecurringQueueName { get; set; }

        
        
        
        public string GlobalSettingJsonFilePath { get; set; } 

        
        
        
        public string DefaultBackGroundJobQueueName { get; set; } = EnqueuedState.DefaultQueue;

        
        
        
        public string Proxy { get; set; }

        
        
        
        public bool EnableJobAgentErrorThrow { get; set; } = true;

        
        
        
        public MailOption MailOption { get; set; } = new MailOption();

        
        
        ///  如果不指定 < 400 = error
        
        public Func<HttpStatusCode, string, bool> CheckHttpResponseStatusCode;


        
        
        
        public Func<HttpJobItem, bool> AddHttpJobFilter;


        #region 按钮名称和标题等自定义展示名称

        public string AddHttpJobButtonName { get; set; } = Strings.AddHttpJobButtonName;
        public string ExportJobsButtonName { get; set; } = Strings.ExportJobsButtonName;
        public string ImportJobsButtonName { get; set; } = Strings.ImportJobsButtonName;
        public string AddRecurringJobHttpJobButtonName { get; set; } = Strings.AddRecurringJobHttpJobButtonName;
        public string CloseButtonName { get; set; } = Strings.CloseButtonName;
        public string SubmitButtonName { get; set; } = Strings.SubmitButtonName;
        public string LogOutButtonName { get; set; } = Strings.LogOutButtonName;
        public string StartBackgroudJobButtonName { get; set; } = Strings.StartBackgroudJobButtonName;
        public string StopBackgroudJobButtonName { get; set; } = Strings.StopBackgroudJobButtonName;
        public string AgentJobDeatilButton { get; set; } = Strings.AgentJobDeatilButton;

        public string SearchPlaceholder { get; set; } = Strings.SearchPlaceholder;
        public string SearchPlaceholderUseName { get; set; } = Strings.SearchPlaceholderUseName;
        public string ScheduledEndPath { get; set; } = "jobs/scheduled";
        public string RecurringEndPath { get; set; } = "/recurring";

        
        
        
        public string AddCronButtonName { get; set; } = Strings.AddCronButtonName;
        public string GobalSettingButtonName { get; set; } = Strings.GobalSettingButtonName;

        public string PauseJobButtonName { get; set; } = Strings.PauseJobButtonName;

        public string EditRecurringJobButtonName { get; set; } = Strings.EditRecurringJobButtonName;



        
        
        
        public string DashboardTitle { get; set; } = Strings.DashboardTitle;
        
        
        
        public string DashboardName { get; set; } = Strings.DashboardName;

        
        
        
        public string DashboardFooter { get; set; } = "Github";

        #endregion

        
        
        
        public DingTalkOption DingTalkOption { get; set; } = new DingTalkOption();

        
        
        
        public bool EnableDingTalk { get; set; } 

        
        
        
        public string CurrentDomain { get; set; }

        
        
        
        public Dictionary<string, object> SpelVarDictionary { get; set; } = new Dictionary<string, object>();

        
        
        
        public GatewayOption GatewayOption { get; set; } = new GatewayOption();

        
        
        
        public RequestOption ApiRequestOption { get; set; } = new RequestOption();

        
        
        
        public OcelotOption OcelotAuthOption { get; set; } = new OcelotOption();
    }

    public class DingTalkOption
    {
        
        
        
        public string Token { get; set; }
        
        
        
        
        public string Secret { get; set; }

        
        
        
        public string AtPhones { get; set; }

        
        ///  通知是否@所有人
        
        public bool IsAtAll { get; set; }
    }



    public class MailOption
    {

        
        
        
        public List<string> AlertMailList { get; set; } = new List<string>();

        
        
        
        public string Server { get; set; }
        
        
        
        public int Port { get; set; }
        
        
        
        public string User { get; set; }

        public bool UseSsl { get; set; }
        
        
        
        public string Password { get; set; }

    }

    
    
    
    public class GatewayOption
    {
        
        
        
        public bool Enable { get; set; }

        
        
        
        public string Host { get; set; }

        
        
        
        public int Port { get; set; }

        
        
        
        public string AppKey { get; set; }

        
        
        
        public string AppSecret { get; set; }

        
        
        
        public int Timeout { get; set; }
    }

    
    
    
    public class RequestOption
    {
        
        
        
        public string Host { get; set; }

        
        
        
        public int DB { get; set; }

        
        
        
        public string Password { get; set; }

        
        
        
        public int Timeout { get; set; }

        
        
        
        public bool IsHttps { get; set; }
    }

    
    
    
    public class GateWayAccessDto
    {
        
        
        
        public string Token { get; set; }

        
        
        
        public int ExpiresIn { get; set; }
    }

    
    
    
    public class OcelotOption
    {
        
        
        
        public string Host { get; set; }

        
        
        
        public string UserName { get; set; }

        
        
        
        public string Password { get; set; }

        
        
        
        public string GrantType { get; set; }

        
        
        
        public string UrlPrefix { get; set; }
    }

    
    
    
    public class OcelotAccessDto
    {
        
        
        
        [JsonProperty("access_token")]
        public string Token { get; set; }

        
        
        
        [JsonProperty("expires_in")]
        public int ExpiresIn { get; set; }

        
        
        
        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        
        
        
        public string Scope { get; set; }
    }
}
