{"format": 1, "restore": {"E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Agent\\Hangfire.HttpJob.Agent.RedisConsole\\Hangfire.HttpJob.Agent.RedisConsole.csproj": {}}, "projects": {"E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Agent\\Hangfire.HttpJob.Agent.RedisConsole\\Hangfire.HttpJob.Agent.RedisConsole.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Agent\\Hangfire.HttpJob.Agent.RedisConsole\\Hangfire.HttpJob.Agent.RedisConsole.csproj", "projectName": "Hangfire.HttpJob.Agent.RedisConsole", "projectPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Agent\\Hangfire.HttpJob.Agent.RedisConsole\\Hangfire.HttpJob.Agent.RedisConsole.csproj", "packagesPath": "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\", "outputPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Agent\\Hangfire.HttpJob.Agent.RedisConsole\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://172.19.50.115:15840/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Agent\\Hangfire.HttpJob.Agent\\Hangfire.HttpJob.Agent.csproj": {"projectPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Agent\\Hangfire.HttpJob.Agent\\Hangfire.HttpJob.Agent.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"StackExchange.Redis": {"target": "Package", "version": "[2.2.79, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Agent\\Hangfire.HttpJob.Agent\\Hangfire.HttpJob.Agent.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Agent\\Hangfire.HttpJob.Agent\\Hangfire.HttpJob.Agent.csproj", "projectName": "Hangfire.HttpJob.Agent", "projectPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Agent\\Hangfire.HttpJob.Agent\\Hangfire.HttpJob.Agent.csproj", "packagesPath": "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\", "outputPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Agent\\Hangfire.HttpJob.Agent\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://172.19.50.115:15840/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Client\\Hangfire.HttpJob.Client\\Hangfire.HttpJob.Client.csproj": {"projectPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Client\\Hangfire.HttpJob.Client\\Hangfire.HttpJob.Client.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Dapper": {"target": "Package", "version": "[1.60.6, )"}, "Microsoft.AspNetCore": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.AspNetCore.Hosting.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[3.1.20, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[3.1.20, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Client\\Hangfire.HttpJob.Client\\Hangfire.HttpJob.Client.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Client\\Hangfire.HttpJob.Client\\Hangfire.HttpJob.Client.csproj", "projectName": "Hangfire.HttpJob.Client", "projectPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Client\\Hangfire.HttpJob.Client\\Hangfire.HttpJob.Client.csproj", "packagesPath": "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\", "outputPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Client\\Hangfire.HttpJob.Client\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://172.19.50.115:15840/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"HttpClientFactory": {"target": "Package", "version": "[1.0.3, )"}, "Microsoft.AspNetCore": {"target": "Package", "version": "[2.2.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}