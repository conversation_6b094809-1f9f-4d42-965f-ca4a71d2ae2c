{"Logging": {"IncludeScopes": false, "LogLevel": {"Default": "Trace", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "JobAgent": {"Enabled": true, "SitemapUrl": "/jobagent", "EnabledBasicAuth": true, "BasicUserName": "admin", "BasicUserPwd": "123456", "EnableAutoRegister": true, "RegisterAgentHost": "http://localhost:5002", "RegisterHangfireUrl": "http://localhost:5000/job", "RegisterHangfireBasicName": "admin", "RegisterHangfireBasicPwd": "test"}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"System": "Information", "Microsoft": "Information", "Microsoft.AspNetCore.Authentication": "Information"}}, "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level}] {SourceContext}{NewLine}{Message:lj}{NewLine}{Exception}{NewLine}", "theme": "Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme::<PERSON><PERSON><PERSON>, Serilog.Sinks.Console"}}, {"Name": "File", "Args": {"path": "logs\\jobagent.log.txt", "rollingInterval": "Day"}}]}}