{"Logging": {"IncludeScopes": false, "LogLevel": {"Default": "Trace", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Hangfire": {"HangfireSettings": {"ServerName": "MysqlHangfire", "TablePrefix": "hangfire", "StartUpPath": "/job", "ReadOnlyPath": "", "JobQueues": ["default", "apis", "recurring", "endpoint_instance_start", "endpoint_activity_turning", "endpoint_activity_completed", "endpoint_instance_end", "tripartite_system_instance_start", "tripartite_system_activity_turning", "tripartite_system_activity_completed", "tripartite_system_instance_end", "todomessage", "othermessage"], "WorkerCount": 40, "DisplayStorageConnectionString": false, "HttpAuthInfo": {"SslRedirect": false, "RequireSsl": false, "LoginCaseSensitive": true, "IsOpenLogin": true, "Users": [{"Login": "bpm", "PasswordClear": "rPFwdOQHXnl5mzCW"}, {"Login": "guest", "PasswordClear": "U0ymicSJzqeC41XZ"}]}, "ConnectionString": "server=*************;Port=3306;Database=hangfire-todocentre1;uid=bpm;Pwd=****************;charset=utf8;SslMode=none;Allow User Variables=True"}, "HttpJobOptions": {"Lang": "zh", "DefaultTimeZone": "Asia/Shanghai", "CurrentDomain": "//", "EnableDingTalk": true, "DefaultRecurringQueueName": "recurring", "GlobalSettingJsonFilePath": "", "Proxy": "", "JobExpirationTimeoutDay": 7, "GlobalHttpTimeOut": 5000, "MailOption": {"Server": "", "Port": 0, "User": "", "Password": "", "UseSsl": false, "AlertMailList": []}, "DingTalkOption": {"Token": "", "AtPhones": "", "IsAtAll": false}, "GatewayOption": {"Enable": false, "Host": "*************", "Port": "32000", "AppKey": "799e6e124ad95e09b055ae8c8cc53d7f", "AppSecret": "a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0"}, "ApiRequestOption": {"Host": "*************:6379", "DB": 8, "Password": "7Qx0YFWyUDsbjzBv", "Timeout": "30", "IsHttps": false}, "OcelotAuthOption": {"Host": "http://*************:32065/connect/Token", "UserName": "Admin", "Password": "12345678", "GrantType": "client_credentials", "UrlPrefix": "integration-ocelot"}, "RabbitMQOption": [{"HostName": "*************", "Port": 5672, "UserName": "bpm", "Password": "1nsc72AZBxNFLtw4", "VirtualHost": "/", "Queues": [{"Name": "todomessage", "NumOfThreads": 3, "UrlReplaces": [{"OldStr": "/todo-centre/", "NewStr": "/todo-centre-dataprocess/"}]}, {"Name": "othermessage", "NumOfThreads": 3, "UrlReplaces": [{"OldStr": "/todo-centre/", "NewStr": "/todo-centre-dataprocess/"}]}, {"Name": "portalmessage", "NumOfThreads": 3, "UrlReplaces": [{"OldStr": "/todo-centre/", "NewStr": "/todo-centre-dataprocess/"}]}, {"Name": "bpm-job-queue", "NumOfThreads": 3, "UrlReplaces": [{"OldStr": "/todo-centre/", "NewStr": "/todo-centre-dataprocess/"}]}]}]}}}