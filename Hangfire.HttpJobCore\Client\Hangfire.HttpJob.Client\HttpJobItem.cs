﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

using Newtonsoft.Json;

namespace Hangfire.HttpJob.Client
{

    internal class BaseHttpJobInfo
    {
        #region HttpJob
        
        
        
        public string Url { get; set; }

        
        
        
        public string Method { get; set; }

        
        
        
        public string Data { get; set; }

        public string ContentType { get; set; }

        public int Timeout { get; set; }

        public int DelayFromMinutes { get; set; }
        public string Cron { get; set; }
        public string JobName { get; set; }
        public string RecurringJobIdentifier { get; set; }
        public string QueueName { get; set; }

        
        
        
        public bool SendSuccess { get; set; }

        
        
        
        public bool SendFail { get; set; }

        
        
        
        public string Mail { get; set; }

        
        
        
        public int RetryTimes { get; set; }
        
        
        
        public bool EnableRetry { get; set; }

        
        
        
        public string RetryDelaysInSeconds { get; set; }

        
        
        
        public string AgentClass { get; set; }

        
        
        
        public Dictionary<string, string> Headers { get; set; } = new Dictionary<string, string>();

        
        
        
        public Dictionary<string, object> QueryParams { get; set; } = new Dictionary<string, object>();

        public string BasicUserName { get; set; }
        public string BasicPassword { get; set; }

        
        
        
        public string CallbackEL { get; set; }

        public BaseHttpJobInfo Success { get; set; }
        public BaseHttpJobInfo Fail { get; set; }
        
        
        
        public string TimeZone { get; set; }

        
        
        
        public DingTalkOption DingTalk { get; set; }
        #endregion
    }
    public class DingTalkOption
    {
        
        
        
        public string Token { get; set; }

        
        
        
        public string AtPhones { get; set; }

        
        ///  通知是否@所有人
        
        public bool IsAtAll { get; set; }
    }

    internal class HttpJobItem : BaseHttpJobInfo
    {
        private readonly string _hangfireUrl;
        private readonly HangfireServerPostOption _httpPostOption;

        private HttpJobItem()
        {
            Method = "Post";
            ContentType = "application/json";
            Timeout = 20000;
            DelayFromMinutes = 15;
        }

        public HttpJobItem(string hangfireUrl, HangfireServerPostOption option) : this()
        {
            _hangfireUrl = hangfireUrl;
            _httpPostOption = option;
        }

        
        
        
        public string JobId { get; set; }

        
        
        
        public string JobKeyWords { get; set; }

        
        
        
        
        public async Task<T> PostAsync<T>() where T : HangfireJobResult, new()
        {
            var result = new T();
            try
            {
                var client = _httpPostOption.HttpClient ?? HangfireJobClient.HangfireJobHttpClientFactory.GetHttpClient(_hangfireUrl);
                var httpMesage = PrepareHttpRequestMessage();
                if (_httpPostOption.TimeOut < 1) _httpPostOption.TimeOut = 5000;
                var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(_httpPostOption.TimeOut));
                var httpResponse = await client.SendAsync(httpMesage, cts.Token);

                if (httpResponse.StatusCode == HttpStatusCode.OK)
                {
                    if (result is AddBackgroundHangfireJobResult br)
                    {
                        br.JobId = await httpResponse.Content.ReadAsStringAsync();
                    }
                    result.IsSuccess = true;
                    return result;
                }

                if (httpResponse.StatusCode == HttpStatusCode.InternalServerError)
                {
                    var err = await httpResponse.Content.ReadAsStringAsync();
                    result.IsSuccess = false;
                    result.ErrMessage = string.IsNullOrEmpty(err) ? httpResponse.StatusCode.ToString() : err;
                    return result;
                }

                if (httpResponse.StatusCode != HttpStatusCode.NoContent)
                {
                    result.IsSuccess = false;
                    result.ErrMessage = httpResponse.StatusCode.ToString();
                    return result;
                }

            }
            catch (Exception ex)
            {
                result.IsSuccess = false;
                result.ErrMessage = ex.Message;
                if (_httpPostOption.ThrowException) throw;
                return result;
            }

            result.IsSuccess = true;
            return result;
        }
        public T Post<T>() where T : HangfireJobResult, new()
        {
            return PostAsync<T>().ConfigureAwait(false).GetAwaiter().GetResult();
        }


        private HttpRequestMessage PrepareHttpRequestMessage()
        {
            var request = new HttpRequestMessage(new HttpMethod("POST"), this._hangfireUrl);
            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            var data = JsonConvert.SerializeObject(this);
            var bytes = Encoding.UTF8.GetBytes(data);
            request.Content = new ByteArrayContent(bytes, 0, bytes.Length);
            if (!string.IsNullOrEmpty(_httpPostOption.BasicUserName) && !string.IsNullOrEmpty(_httpPostOption.BasicPassword))
            {
                var byteArray = Encoding.ASCII.GetBytes(_httpPostOption.BasicUserName + ":" + _httpPostOption.BasicPassword);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));
            }
            return request;
        }


    }
}
