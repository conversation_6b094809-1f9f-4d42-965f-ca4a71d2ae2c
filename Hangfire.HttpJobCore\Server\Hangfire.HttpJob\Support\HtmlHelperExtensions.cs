﻿using Hangfire.Dashboard;
using System;
using System.Reflection;

namespace Hangfire.HttpJob.Support
{
    
    
    
    internal static class HtmlHelperExtensions
    {
        private static readonly FieldInfo _page = typeof(HtmlHelper).GetTypeInfo().GetDeclaredField(nameof(_page));

        
        
        
        
        public static RazorPage GetPage(this HtmlHelper helper)
        {
            if (helper == null)
                throw new ArgumentNullException(nameof(helper));

            return (RazorPage)_page.GetValue(helper);
        }
    }
}
