using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using Hangfire.Dashboard;
using Hangfire.HttpJob.Server;
using Hangfire.Logging;
using Newtonsoft.Json;
using Spring.Core.TypeConversion;
using StackExchange.Redis;

namespace Hangfire.HttpJob.Support
{
    public class CodingUtil
    {
        private static readonly ILog Logger = LogProvider.For<CodingUtil>();

        
        ///判断是否引用了tag服务
        
        public static bool IsTagServiceInstalled = DashboardRoutes.Routes.FindDispatcher("/tags/all") != null;

        
        
        
        public static HangfireHttpJobOptions HangfireHttpJobOptions = new HangfireHttpJobOptions();

        
        
        
        public static Action<HttpJobItem> addJobExtend = null;

        
        
        
        public static Action<long, string, string> updateJobExtend = null;


        
        
        
        private static DateTime? _appJsonLastWriteTime;

        
        
        
        internal static Dictionary<string, object> _appsettingsJson = new Dictionary<string, object>();

        private static readonly ConcurrentDictionary<string, string> _apiRoutes = new ConcurrentDictionary<string, string>();

        
        
        
        
        public static Dictionary<string, object> GetGlobalAppsettings()
        {
            var jsonFile = new FileInfo(HangfireHttpJobOptions.GlobalSettingJsonFilePath);
            if (jsonFile.Exists && (_appJsonLastWriteTime == null || _appJsonLastWriteTime != jsonFile.LastWriteTime))
            {
                _appJsonLastWriteTime = jsonFile.LastWriteTime;
                try
                {
                    _appsettingsJson = JsonConvert.DeserializeObject<Dictionary<string, object>>(File.ReadAllText(jsonFile.FullName));
                }
                catch (Exception e)
                {
                    Logger.WarnException($"HangfireHttpJobOptions.GlobalSettingJsonFilePath read fail", e);
                }
            }

            return _appsettingsJson??new Dictionary<string, object>();
        }

        
        
        
        
        
        public static string GetCurrentJobDetailUrl(string jobId)
        {
            //优先使用全局配置里面的参数
            CodingUtil.GetGlobalAppsettings().TryGetValue("CurrentDomain", out var currentDomain);

            var logDetail = currentDomain != null && !string.IsNullOrEmpty(currentDomain.ToString()) ? $"{currentDomain}/jobs/details/{jobId}" : string.IsNullOrEmpty(CodingUtil.HangfireHttpJobOptions.CurrentDomain) ? $"JobId:{jobId}" : $"{CodingUtil.HangfireHttpJobOptions.CurrentDomain}/jobs/details/{jobId}";

            return logDetail;
        }

        public static ConcurrentDictionary<string, string> GetApiRoutes()
        {
            if(_apiRoutes.Count > 0)
            {
                return _apiRoutes;
            }
            var options = HangfireHttpJobOptions.ApiRequestOption;
            var redisUrl = string.IsNullOrEmpty(options.Password)
                ? options.Host
                : $"{options.Host},password={options.Password}";
            var redisDb = ConnectionMultiplexer.Connect(redisUrl).GetDatabase(options.DB);
            var routes = redisDb.HashGetAll("_routes");
            if (routes == null)
            {
                return _apiRoutes;
            }
            foreach (var route in routes)
            {
                _apiRoutes.TryAdd(route.Name, route.Value);
            }
            return _apiRoutes;
        }

        
        
        
        
        public static bool IgnoreJobAgentSingletonMultExcuteError()
        {
            //优先使用全局配置里面的参数
            return CodingUtil.GetGlobalAppsettings().TryGetValue("IgnoreJobAgentSingletonMultExcuteError", out var value) && value is bool dd && dd ;  
        }

        
        
        
        
        public static bool DingTalkErrReportSimplify()
        {
            return CodingUtil.GetGlobalAppsettings().TryGetValue("EnableDingTalkErrReportSimplify", out var value) && value is bool dd && dd;
        }

        
        
        
        
        public static long JobTimeoutDays()
        {
            var timeoutDays = 0L;
            //如果在全局配置页面有配置的话优先使用这个配置
            if (!GetGlobalAppsettings().TryGetValue("JobTimeoutDays", out var value))
            {
                timeoutDays = 0L;
            }
            else
            {
                if (value is long)
                {
                    timeoutDays = (long)value;
                }
            }

            return timeoutDays > 0 ? timeoutDays :
                HangfireHttpJobOptions.JobExpirationTimeoutDay < 1 ? 1L : HangfireHttpJobOptions.JobExpirationTimeoutDay;
        }

        
        
        
        
        
        
        
        public static T GetGlobalAppsetting<T>(string value, T deflaultValue)
        {
            try
            {
                if (_appsettingsJson!=null && _appsettingsJson.TryGetValue(value, out var v))
                {
                    return (T)TypeConversionUtils.ConvertValueIfNecessary(typeof(T), v, null);
                }
            }
            catch (Exception)
            {
                //ignore
            }
            return deflaultValue;
        }

        
        
        
        
        
        public static bool TryGetGlobalProxy(out string proxy)
        {
            proxy = GetGlobalAppsetting("GlobalProxy", "");
            if (string.IsNullOrEmpty(proxy))
            {
                proxy = HangfireHttpJobOptions.Proxy;
            }

            return !string.IsNullOrEmpty(proxy);
        }
        
        
        
        
        
        
        public static string MD5(string str)
        {
            byte[] b = Encoding.UTF8.GetBytes(str);
            b = new MD5CryptoServiceProvider().ComputeHash(b);
            string ret = string.Empty;
            for (int i = 0; i < b.Length; i++)
            {
                ret += b[i].ToString("x").PadLeft(2, '0');
            }
            return ret;
        }

        public static T FromJson<T>(string json)
        {
            try
            {
                return JsonConvert.DeserializeObject<T>(json);
            }
            catch (Exception)
            {
                return default(T);
            }
        }
    }
}
