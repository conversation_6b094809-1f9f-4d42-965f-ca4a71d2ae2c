{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.1": {"Hangfire.HttpJob.Agent.RedisConsole/1.0.0": {"dependencies": {"Hangfire.HttpJob.Agent": "1.0.0", "StackExchange.Redis": "2.2.79"}, "runtime": {"Hangfire.HttpJob.Agent.RedisConsole.dll": {}}}, "Dapper/1.60.6": {"dependencies": {"System.Data.SqlClient": "4.4.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0"}, "runtime": {"lib/netstandard2.0/Dapper.dll": {"assemblyVersion": "********", "fileVersion": "1.60.6.27094"}}}, "HttpClientFactory/1.0.3": {"runtime": {"lib/netstandard2.0/HttpClientFactory.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.AspNetCore/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Diagnostics": "2.2.0", "Microsoft.AspNetCore.HostFiltering": "2.2.0", "Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Routing": "2.2.0", "Microsoft.AspNetCore.Server.IIS": "2.2.0", "Microsoft.AspNetCore.Server.IISIntegration": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.2.0", "Microsoft.Extensions.Configuration.CommandLine": "2.2.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.Configuration.Json": "2.2.0", "Microsoft.Extensions.Configuration.UserSecrets": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Logging.Configuration": "2.2.0", "Microsoft.Extensions.Logging.Console": "2.2.0", "Microsoft.Extensions.Logging.Debug": "2.2.0", "Microsoft.Extensions.Logging.EventSource": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "3.1.20"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Connections.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.IO.Pipelines": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Diagnostics/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Diagnostics.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "3.1.20", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Reflection.Metadata": "1.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.HostFiltering/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Options": "3.1.20"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.HostFiltering.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Configuration": "3.1.20", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.2.0", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Microsoft.Extensions.DependencyInjection": "2.2.0", "Microsoft.Extensions.FileProviders.Physical": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Options": "3.1.20", "System.Diagnostics.DiagnosticSource": "4.5.0", "System.Reflection.Metadata": "1.6.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.Hosting.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "Microsoft.Extensions.Configuration.Abstractions": "3.1.20"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "3.1.20", "Microsoft.Net.Http.Headers": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Net.Http.Headers": "2.2.0", "System.Buffers": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.20"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.HttpOverrides/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "3.1.20"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.HttpOverrides.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Routing/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.Routing.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.ObjectPool": "2.2.0", "Microsoft.Extensions.Options": "3.1.20"}, "runtime": {"lib/netcoreapp2.2/Microsoft.AspNetCore.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Routing.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Server.IIS/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Connections.Abstractions": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "System.IO.Pipelines": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.IIS.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}, "runtimeTargets": {"runtimes/win-x64/nativeassets/netcoreapp2.2/aspnetcorev2_inprocess.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "12.2.18316.0"}, "runtimes/win-x86/nativeassets/netcoreapp2.2/aspnetcorev2_inprocess.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "12.2.18316.0"}}}, "Microsoft.AspNetCore.Server.IISIntegration/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Http.Extensions": "2.2.0", "Microsoft.AspNetCore.HttpOverrides": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "3.1.20", "System.Buffers": "4.5.0", "System.IO.Pipelines": "5.0.0", "System.Memory": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.1", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.IISIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Server.Kestrel/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Http": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.2.0", "Microsoft.AspNetCore.WebUtilities": "2.2.0", "Microsoft.Extensions.Configuration.Binder": "3.1.20", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "3.1.20", "Microsoft.Net.Http.Headers": "2.2.0", "System.Memory": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.1", "System.Security.Cryptography.Cng": "4.5.0", "System.Threading.Tasks.Extensions": "4.5.1"}, "runtime": {"lib/netcoreapp2.1/Microsoft.AspNetCore.Server.Kestrel.Core.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.2.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.AspNetCore.Server.Kestrel.Https.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/2.2.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "3.1.20"}, "runtime": {"lib/netcoreapp2.1/Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"dependencies": {"Microsoft.Net.Http.Headers": "2.2.0", "System.Text.Encodings.Web": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Extensions.Configuration/3.1.20": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.20"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "********", "fileVersion": "3.100.2021.47203"}}}, "Microsoft.Extensions.Configuration.Abstractions/3.1.20": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.20"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.2021.47203"}}}, "Microsoft.Extensions.Configuration.Binder/3.1.20": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.20"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "********", "fileVersion": "3.100.2021.47203"}}}, "Microsoft.Extensions.Configuration.CommandLine/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.20"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.20"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Configuration.FileExtensions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.20", "Microsoft.Extensions.FileProviders.Physical": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Configuration.Json/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration": "3.1.20", "Microsoft.Extensions.Configuration.FileExtensions": "2.2.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Configuration.UserSecrets/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.DependencyInjection/2.2.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.20"}, "runtime": {"lib/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.20": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.2021.47203"}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.20"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.FileProviders.Physical/2.2.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.FileSystemGlobbing": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.FileSystemGlobbing/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.20", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.20", "Microsoft.Extensions.FileProviders.Abstractions": "2.2.0", "Microsoft.Extensions.Logging.Abstractions": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.Extensions.Logging/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "3.1.20", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.20", "Microsoft.Extensions.Logging.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "3.1.20"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Logging.Abstractions/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Logging.Configuration/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.20"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Logging.Console/2.2.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.20", "Microsoft.Extensions.Logging": "2.2.0", "Microsoft.Extensions.Logging.Configuration": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Logging.Debug/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Logging.EventSource/2.2.0": {"dependencies": {"Microsoft.Extensions.Logging": "2.2.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.ObjectPool/2.2.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18315"}}}, "Microsoft.Extensions.Options/3.1.20": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.20", "Microsoft.Extensions.Primitives": "3.1.20"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.dll": {"assemblyVersion": "********", "fileVersion": "3.100.2021.47203"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/3.1.20": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "3.1.20", "Microsoft.Extensions.Configuration.Binder": "3.1.20", "Microsoft.Extensions.DependencyInjection.Abstractions": "3.1.20", "Microsoft.Extensions.Options": "3.1.20"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "********", "fileVersion": "3.100.2021.47203"}}}, "Microsoft.Extensions.Primitives/3.1.20": {"runtime": {"lib/netcoreapp3.1/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "3.100.2021.47203"}}}, "Microsoft.Net.Http.Headers/2.2.0": {"dependencies": {"Microsoft.Extensions.Primitives": "3.1.20", "System.Buffers": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.18316"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Win32.SystemEvents/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.1.25517"}}}, "Pipelines.Sockets.Unofficial/2.2.0": {"dependencies": {"System.IO.Pipelines": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/Pipelines.Sockets.Unofficial.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.0.45337"}}}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "StackExchange.Redis/2.2.79": {"dependencies": {"Pipelines.Sockets.Unofficial": "2.2.0", "System.Diagnostics.PerformanceCounter": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/StackExchange.Redis.dll": {"assemblyVersion": "*******", "fileVersion": "2.2.79.4591"}}}, "System.Buffers/4.5.0": {}, "System.Configuration.ConfigurationManager/5.0.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "5.0.0", "System.Security.Permissions": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Data.SqlClient/4.4.0": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "4.4.0", "runtime.native.System.Data.SqlClient.sni": "4.4.0"}, "runtime": {"lib/netstandard2.0/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard2.0/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}, "runtimes/win/lib/netstandard2.0/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Diagnostics.DiagnosticSource/4.5.0": {}, "System.Diagnostics.PerformanceCounter/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.Win32.Registry": "5.0.0", "System.Configuration.ConfigurationManager": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Drawing.Common/5.0.0": {"dependencies": {"Microsoft.Win32.SystemEvents": "5.0.0"}, "runtime": {"lib/netcoreapp3.0/System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/win/lib/netcoreapp3.0/System.Drawing.Common.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Pipelines/5.0.0": {"runtime": {"lib/netcoreapp3.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Memory/4.5.1": {}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/1.6.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.4.0": {}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/4.5.1": {}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.ProtectedData/5.0.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Permissions/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Windows.Extensions": "5.0.0"}, "runtime": {"lib/netcoreapp3.0/System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Security.Principal.Windows/5.0.0": {"runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/4.4.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0"}}, "System.Text.Encodings.Web/4.5.0": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.1": {}, "System.Windows.Extensions/5.0.0": {"dependencies": {"System.Drawing.Common": "5.0.0"}, "runtime": {"lib/netcoreapp3.0/System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp3.0/System.Windows.Extensions.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Hangfire.HttpJob.Agent/1.0.0": {"dependencies": {"Dapper": "1.60.6", "Hangfire.HttpJob.Client": "1.0.0", "Microsoft.AspNetCore": "2.2.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.2.0", "Microsoft.Extensions.Options": "3.1.20", "Microsoft.Extensions.Options.ConfigurationExtensions": "3.1.20", "Newtonsoft.Json": "13.0.1"}, "runtime": {"Hangfire.HttpJob.Agent.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Hangfire.HttpJob.Client/1.0.0": {"dependencies": {"HttpClientFactory": "1.0.3", "Microsoft.AspNetCore": "2.2.0"}, "runtime": {"Hangfire.HttpJob.Client.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"Hangfire.HttpJob.Agent.RedisConsole/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dapper/1.60.6": {"type": "package", "serviceable": true, "sha512": "sha512-mmnJNhKMeF2KhvVXDoVQlFxre8aJAo71YBJrKqFlvuqzYC2QiXUq94/GCDBJzU7paq4GqpkV2glw3308TcGibw==", "path": "dapper/1.60.6", "hashPath": "dapper.1.60.6.nupkg.sha512"}, "HttpClientFactory/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-12pNr6uQoKv1FJnguWJ1WuKFCS+z5DrYW1twF25Ix2w2w8houGYUja4VPNkPRUXojEsLD46t68BnuDLirtBB9Q==", "path": "httpclientfactory/1.0.3", "hashPath": "httpclientfactory.1.0.3.nupkg.sha512"}, "Microsoft.AspNetCore/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bs75iht4lXS8uVWy/Cbsr9i0m2jRtnrfPEWU+6t0dQTZcJEfF9b7G2F7XvstLFWkAKSgYRzFkAwi/KypY0Qtew==", "path": "microsoft.aspnetcore/2.2.0", "hashPath": "microsoft.aspnetcore.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-VloMLDJMf3n/9ic5lCBOa42IBYJgyB1JhzLsL68Zqg+2bEPWfGBj/xCJy/LrKTArN0coOcZp3wyVTZlx0y9pHQ==", "path": "microsoft.aspnetcore.authentication.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-XlVJzJ5wPOYW+Y0J6Q/LVTEyfS4ssLXmt60T0SPP+D8abVhBTl+cgw2gDHlyKYIkcJg7btMVh383NDkMVqD/fg==", "path": "microsoft.aspnetcore.authentication.core/2.2.0", "hashPath": "microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Aqr/16Cu5XmGv7mLKJvXRxhhd05UJ7cTTSaUV4MZ3ynAzfgWjsAdpIU8FWuxwAjmVdmI8oOWuVDrbs+sRkhKnA==", "path": "microsoft.aspnetcore.connections.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.connections.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-RobNuZecn/eefWVApOE+OWAZXCdgfzm8pB7tBvJkahsjWfn1a+bLM9I2cuKlp/9aFBok1O/oDXlgYSvaQYu/yg==", "path": "microsoft.aspnetcore.diagnostics/2.2.0", "hashPath": "microsoft.aspnetcore.diagnostics.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pva9ggfUDtnJIKzv0+wxwTX7LduDx6xLSpMqWwdOJkW52L0t31PI78+v+WqqMpUtMzcKug24jGs3nTFpAmA/2g==", "path": "microsoft.aspnetcore.diagnostics.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.diagnostics.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.HostFiltering/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-JSX6ZlVWDkokZ+xCKDhUVQNqbmFn1lHQNzJc8K4Y/uTUocZS83+b/8Q7y/yx3oJ362etGMVy0keAvmCdqbP8nA==", "path": "microsoft.aspnetcore.hostfiltering/2.2.0", "hashPath": "microsoft.aspnetcore.hostfiltering.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7t4RbUGugpHtQmzAkc9fpDdYJg6t/jcB2VVnjensVYbZFnLDU8pNrG0hrekk1DQG7P2UzpSqKLzDsFF0/lkkbw==", "path": "microsoft.aspnetcore.hosting/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ubycklv+ZY7Kutdwuy1W4upWcZ6VFR8WUXU7l7B2+mvbDBBPAcfpi+E+Y5GFe+Q157YfA3C49D2GCjAZc7Mobw==", "path": "microsoft.aspnetcore.hosting.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1PMijw8RMtuQF60SsD/JlKtVfvh4NORAhF4wjysdABhlhTrYmtgssqyncR0Stq5vqtjplZcj6kbT4LRTglt9IQ==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YogBSMotWPAS/X5967pZ+yyWPQkThxhmzAwyCHCSSldzYBkW5W5d6oPfBaPqQOnSHYTpSOSOkpZoAce0vwb6+A==", "path": "microsoft.aspnetcore.http/2.2.0", "hashPath": "microsoft.aspnetcore.http.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxs7Z1q3f1STfLYKJSVXCs1iBl+Ya6E8o4Oy1bCxJ/rNI44E/0f6tbsrVqAWfB7jlnJfyaAtIalBVxPKUPQb4Q==", "path": "microsoft.aspnetcore.http.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2DgZ9rWrJtuR7RYiew01nGRzuQBDaGHGmK56Rk54vsLLsCdzuFUPqbDTJCS1qJQWTbmbIQ9wGIOjpxA1t0l7/w==", "path": "microsoft.aspnetcore.http.extensions/2.2.0", "hashPath": "microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ziFz5zH8f33En4dX81LW84I6XrYXKf9jg6aM39cM+LffN9KJahViKZ61dGMSO2gd3e+qe5yBRwsesvyqlZaSMg==", "path": "microsoft.aspnetcore.http.features/2.2.0", "hashPath": "microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.HttpOverrides/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pOlLQyNKQduGbtbgB55RyTHFeshSfKi3DmofrVjk+UBQjyp+Tm0RNNJFQf+sv34hlFsel+VnD79QyO9Zk/c3oA==", "path": "microsoft.aspnetcore.httpoverrides/2.2.0", "hashPath": "microsoft.aspnetcore.httpoverrides.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jAhDBy0wryOnMhhZTtT9z63gJbvCzFuLm8yC6pHzuVu9ZD1dzg0ltxIwT4cfwuNkIL/TixdKsm3vpVOpG8euWQ==", "path": "microsoft.aspnetcore.routing/2.2.0", "hashPath": "microsoft.aspnetcore.routing.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-lRRaPN7jDlUCVCp9i0W+PB0trFaKB0bgMJD7hEJS9Uo4R9MXaMC8X2tJhPLmeVE3SGDdYI4QNKdVmhNvMJGgPQ==", "path": "microsoft.aspnetcore.routing.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.IIS/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-6NEwFAJFrnZ0f5eJB1ReIpgPM1ZRDj3IE3Rda01nD3vJANCyJFjZ4SGW3Ckn1AmMi225fGflWzpCKLb7/l43jw==", "path": "microsoft.aspnetcore.server.iis/2.2.0", "hashPath": "microsoft.aspnetcore.server.iis.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.IISIntegration/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iVjgAg+doTTrTFCOq6kZRpebXq94YGCx9efMIwO5QhwdY/sHAjfrVz2lXzji63G96YjJVK3ZRrlpgS2fd49ABw==", "path": "microsoft.aspnetcore.server.iisintegration/2.2.0", "hashPath": "microsoft.aspnetcore.server.iisintegration.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-D0vGB8Tp0UNMiAhT+pwAVeqDDx2OFrfpu/plwm0WhA+1DZvTLc99eDwGISL6LAY8x7a12lhl9w7/m+VdoyDu8Q==", "path": "microsoft.aspnetcore.server.kestrel/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-F6/Vesd3ODq/ISbHfcvfRf7IzRtTvrNX8VA36Knm5e7bteJhoRA2GKQUVQ+neoO1njLvaQKnjcA3rdCZ6AF6cg==", "path": "microsoft.aspnetcore.server.kestrel.core/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.core.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-nEH5mU6idUYS3/+9BKw2stMOM25ZdGwIH4P4kyj6PVkMPgQUTkBQ7l/ScPkepdhejcOlPa+g3+M4dYsSYPUJ8g==", "path": "microsoft.aspnetcore.server.kestrel.https/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.https.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-j1ai2CG8BGp4mYf2TWSFjjy1pRgW9XbqhdR4EOVvrlFVbcpEPfXNIPEdjkcgK+txWCupGzkFnFF8oZsASMtmyw==", "path": "microsoft.aspnetcore.server.kestrel.transport.abstractions/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-qTACI0wePgAKCH+YKrMgChyfqJpjwgGZEtSuwBw6TjWLQ66THGasleia/7EZz2t2eAjwWxw8RA/D8ODrBqpj9A==", "path": "microsoft.aspnetcore.server.kestrel.transport.sockets/2.2.0", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.sockets.2.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-9ErxAAKaDzxXASB/b5uLEkLgUWv1QbeVxyJYEHQwMaxXOeFFVkQxiq8RyfVcifLU7NR0QY0p3acqx4ZpYfhHDg==", "path": "microsoft.aspnetcore.webutilities/2.2.0", "hashPath": "microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/3.1.20": {"type": "package", "serviceable": true, "sha512": "sha512-qN871hvjHs9pqVW1E8dXzww3hDXmAtSC0Mjvht+2choJN+KKLL/mQpX2Egkp9Wvox005bfmBrFW7svDDTjmuoQ==", "path": "microsoft.extensions.configuration/3.1.20", "hashPath": "microsoft.extensions.configuration.3.1.20.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/3.1.20": {"type": "package", "serviceable": true, "sha512": "sha512-TpevBA1qF8XuuP5md8As81SHfhtAsVocH/i76F1WjYoOBpA9nwC3dmpN/YDi89efRzU6qk5AYvZ1ldCONtAYSQ==", "path": "microsoft.extensions.configuration.abstractions/3.1.20", "hashPath": "microsoft.extensions.configuration.abstractions.3.1.20.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/3.1.20": {"type": "package", "serviceable": true, "sha512": "sha512-eS6Q5oRKmBQKlgIjSXmgaF2jwSAuii+3UjTSN4jI2LH1N0utPNgZNtnOVXDU2tZiUOtQuAROBb8PZKgHgIgsYQ==", "path": "microsoft.extensions.configuration.binder/3.1.20", "hashPath": "microsoft.extensions.configuration.binder.3.1.20.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-4kJIGOSRqD1Ccqerst4t/zsNs51plR7BIxbdKO1J/9rL+2DuNT+ieAuEv+HROelqTam3yOpKFR7TtHBt3oLpOA==", "path": "microsoft.extensions.configuration.commandline/2.2.0", "hashPath": "microsoft.extensions.configuration.commandline.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gIqt9PkKO01hZ0zmHnWrZ1E45MDreZTVoyDbL1kMWKtDgxxWTJpYtESTEcgpvR1uB1iex1zKGYzJpOMgmuP5TQ==", "path": "microsoft.extensions.configuration.environmentvariables/2.2.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-H1qCpWBC8Ed4tguTR/qYkbb3F6DI5Su3t8xyFo3/5MzAd8PwPpHzgX8X04KbBxKmk173Pb64x7xMHarczVFQUA==", "path": "microsoft.extensions.configuration.fileextensions/2.2.0", "hashPath": "microsoft.extensions.configuration.fileextensions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-jUDdmLyFmLf9V3mqnMzSAzAv4QigJ67tZh5Q7HBXeBnESL2UyeesNG6jSBti+b63JpxZf+EDyn+anx3gyrNxug==", "path": "microsoft.extensions.configuration.json/2.2.0", "hashPath": "microsoft.extensions.configuration.json.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-2/N2xo6/sNbVshnKktmq5lwaQbsAR2SrzCVrJEeMP8OKZVI7SzT8P6/WXZF8/YC7dTYsMe3nrHzgl1cF9i5ZKQ==", "path": "microsoft.extensions.configuration.usersecrets/2.2.0", "hashPath": "microsoft.extensions.configuration.usersecrets.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-MZtBIwfDFork5vfjpJdG5g8wuJFt7d/y3LOSVVtDK/76wlbtz6cjltfKHqLx2TKVqTj5/c41t77m1+h20zqtPA==", "path": "microsoft.extensions.dependencyinjection/2.2.0", "hashPath": "microsoft.extensions.dependencyinjection.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/3.1.20": {"type": "package", "serviceable": true, "sha512": "sha512-a2axLm7TfsB6rELiYDp7qx0S64h1FCFAFGz0WnPWgyshpvLWYM/XKwLHIPqiXuhtEp9kT4qBXRYsXMe6ZrxX0A==", "path": "microsoft.extensions.dependencyinjection.abstractions/3.1.20", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.3.1.20.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-EcnaSsPTqx2MGnHrmWOD0ugbuuqVT8iICqSqPzi45V5/MA1LjUNb0kwgcxBGqizV1R+WeBK7/Gw25Jzkyk9bIw==", "path": "microsoft.extensions.fileproviders.abstractions/2.2.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-tbDHZnBJkjYd9NjlRZ9ondDiv1Te3KYCTW2RWpR1B0e1Z8+EnFRo7qNnHkkSCixLdlPZzhjlX24d/PixQ7w2dA==", "path": "microsoft.extensions.fileproviders.physical/2.2.0", "hashPath": "microsoft.extensions.fileproviders.physical.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZSsHZp3PyW6vk37tDEdypjgGlNtpJ0EixBMOfUod2Thx7GtwfFSAQXUQx8a8BN8vfWKGGMbp7jPWdoHx/At4wQ==", "path": "microsoft.extensions.filesystemglobbing/2.2.0", "hashPath": "microsoft.extensions.filesystemglobbing.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-+k4AEn68HOJat5gj1TWa6X28WlirNQO9sPIIeQbia+91n03esEtMSSoekSTpMjUzjqtJWQN3McVx0GvSPFHF/Q==", "path": "microsoft.extensions.hosting.abstractions/2.2.0", "hashPath": "microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nxqhadc9FCmFHzU+fz3oc8sFlE6IadViYg8dfUdGzJZ2JUxnCsRghBhhOWdM4B2zSZqEc+0BjliBh/oNdRZuig==", "path": "microsoft.extensions.logging/2.2.0", "hashPath": "microsoft.extensions.logging.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-B2WqEox8o+4KUOpL7rZPyh6qYjik8tHi2tN8Z9jZkHzED8ElYgZa/h6K+xliB435SqUcWT290Fr2aa8BtZjn8A==", "path": "microsoft.extensions.logging.abstractions/2.2.0", "hashPath": "microsoft.extensions.logging.abstractions.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-ukU1mQGX9+xBsEzpNd13yl4deFVYI+fxxnmKpOhvNZsF+/trCrAUQh+9QM5pPGHbfYkz3lLQ4BXfKCP0502dLw==", "path": "microsoft.extensions.logging.configuration/2.2.0", "hashPath": "microsoft.extensions.logging.configuration.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-1eGgcOJ++PMxW6sn++j6U7wsWvhEBm/5ScqBUUBGLRE8M7AHahi9tsxivDMqEXVM3F0/pshHl3kEpMXtw4BeFg==", "path": "microsoft.extensions.logging.console/2.2.0", "hashPath": "microsoft.extensions.logging.console.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-JjqWtshxUujSnxslFccCRAaH8uFOciqXkYdRw+h5MwpC4sUc+ju9yZzvVi6PA5vW09ckv26EkasEvXrofGiaJg==", "path": "microsoft.extensions.logging.debug/2.2.0", "hashPath": "microsoft.extensions.logging.debug.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-oOa5H+vdNgpsxE6vgtX4U/godKtX2edVi+QjlWb2PBQfavGIQ3WxtjxN+B0DQAjwBNdV4mW8cgOiDEZ8KdR7Ig==", "path": "microsoft.extensions.logging.eventsource/2.2.0", "hashPath": "microsoft.extensions.logging.eventsource.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gA8H7uQOnM5gb+L0uTNjViHYr+hRDqCdfugheGo/MxQnuHzmhhzCBTIPm19qL1z1Xe0NEMabfcOBGv9QghlZ8g==", "path": "microsoft.extensions.objectpool/2.2.0", "hashPath": "microsoft.extensions.objectpool.2.2.0.nupkg.sha512"}, "Microsoft.Extensions.Options/3.1.20": {"type": "package", "serviceable": true, "sha512": "sha512-K5h3xUrYP8mbGZeGAm/vcWjol2wBh2V1vV+Vz02DCKlZ/99Y8ecKJwdpH+elfdqcEFXy76jk+I1nBsmhPKeCgw==", "path": "microsoft.extensions.options/3.1.20", "hashPath": "microsoft.extensions.options.3.1.20.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/3.1.20": {"type": "package", "serviceable": true, "sha512": "sha512-vmh27AY00NDg6+4P5NbLnhKsrNMBtfcFAoE0Pim7yNAB46ev44vu2O5a3AINUoRl9Kovik72Wgn8qA4IpQu+vg==", "path": "microsoft.extensions.options.configurationextensions/3.1.20", "hashPath": "microsoft.extensions.options.configurationextensions.3.1.20.nupkg.sha512"}, "Microsoft.Extensions.Primitives/3.1.20": {"type": "package", "serviceable": true, "sha512": "sha512-RHHWUHzW8y+dyNBIBmo2EQbpCC6xFQcFMpLhNcpzw3zP0rxJdhmTTdy5eXvhlkNi3vqM4Af5Qqb5xgYwqaoaJQ==", "path": "microsoft.extensions.primitives/3.1.20", "hashPath": "microsoft.extensions.primitives.3.1.20.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-iZNkjYqlo8sIOI0bQfpsSoMTmB/kyvmV2h225ihyZT33aTp48ZpF6qYnXxzSXmHt8DpBAwBTX+1s1UFLbYfZKg==", "path": "microsoft.net.http.headers/2.2.0", "hashPath": "microsoft.net.http.headers.2.2.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bh6blKG8VAKvXiLe2L+sEsn62nc1Ij34MrNxepD2OCrS5cpCwQa9MeLyhVQPQ/R4Wlzwuy6wMK8hLb11QPDRsQ==", "path": "microsoft.win32.systemevents/5.0.0", "hashPath": "microsoft.win32.systemevents.5.0.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Pipelines.Sockets.Unofficial/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-7hzHplEIVOGBl5zOQZGX/DiJDHjq+RVRVrYgDiqXb6RriqWAdacXxp+XO9WSrATCEXyNOUOQg9aqQArsjase/A==", "path": "pipelines.sockets.unofficial/2.2.0", "hashPath": "pipelines.sockets.unofficial.2.2.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-A8v6PGmk+UGbfWo5Ixup0lPM4swuSwOiayJExZwKIOjTlFFQIsu3QnDXECosBEyrWSPryxBVrdqtJyhK3BaupQ==", "path": "runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "StackExchange.Redis/2.2.79": {"type": "package", "serviceable": true, "sha512": "sha512-cVk+SkWcfbeLHdCbaqzRwihBy/FkahpucmMHD7nr6364WGcy83aSSYEtksYByRY2z2AIBtsP2tj5ZtRAuNIARw==", "path": "stackexchange.redis/2.2.79", "hashPath": "stackexchange.redis.2.2.79.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aM7cbfEfVNlEEOj3DsZP+2g9NRwbkyiAv2isQEzw7pnkDg9ekCU2m1cdJLM02Uq691OaCS91tooaxcEn8d0q5w==", "path": "system.configuration.configurationmanager/5.0.0", "hashPath": "system.configuration.configurationmanager.5.0.0.nupkg.sha512"}, "System.Data.SqlClient/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-fxb9ghn1k1Ua7FFdlvtiBOD4/PsQvD/fk2KnhS+FK7VC6OggEx6P+lP1P0+KMb5V2dqS1+FbR7HCenoqzJMNIA==", "path": "system.data.sqlclient/4.4.0", "hashPath": "system.data.sqlclient.4.4.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIHRELiYDQvsMToML81QFkXEEYXUSUT2F28t1SGrevWqP+epFdw80SyAXIKTXOHrIEXReFOEnEr7XlGiC2GgOg==", "path": "system.diagnostics.diagnosticsource/4.5.0", "hashPath": "system.diagnostics.diagnosticsource.4.5.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kcQWWtGVC3MWMNXdMDWfrmIlFZZ2OdoeT6pSNVRtk9+Sa7jwdPiMlNwb0ZQcS7NRlT92pCfmjRtkSWUW3RAKwg==", "path": "system.diagnostics.performancecounter/5.0.0", "hashPath": "system.diagnostics.performancecounter.5.0.0.nupkg.sha512"}, "System.Drawing.Common/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SztFwAnpfKC8+sEKXAFxCBWhKQaEd97EiOL7oZJZP56zbqnLpmxACWA8aGseaUExciuEAUuR9dY8f7HkTRAdnw==", "path": "system.drawing.common/5.0.0", "hashPath": "system.drawing.common.5.0.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-irMYm3vhVgRsYvHTU5b2gsT2CwT/SMM6LZFzuJjpIvT5Z4CshxNsaoBC1X/LltwuR3Opp8d6jOS/60WwOb7Q2Q==", "path": "system.io.pipelines/5.0.0", "hashPath": "system.io.pipelines.5.0.0.nupkg.sha512"}, "System.Memory/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-sDJYJpGtTgx+23Ayu5euxG5mAXWdkDb4+b0rD0Cab0M1oQS9H0HXGPriKcqpXuiJDTV7fTp/d+fMDJmnr6sNvA==", "path": "system.memory/4.5.1", "hashPath": "system.memory.4.5.1.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkmh/ySlwnXJp/1qYP9uyKkCK1CXR/REFzl7abHcArxBcV91mY2CgrrzSRA5Z/X4MevJWwXsklGRdR3A7K9zbg==", "path": "system.reflection.typeextensions/4.4.0", "hashPath": "system.reflection.typeextensions.4.4.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Zh8t8oqolRaFa9vmOZfdQm/qKejdqz0J9kr7o2Fu0vPeoH3BL1EOXipKWwkWtLT1JPzjByrF19fGuFlNbmPpiw==", "path": "system.runtime.compilerservices.unsafe/4.5.1", "hashPath": "system.runtime.compilerservices.unsafe.4.5.1.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HGxMSAFAPLNoxBvSfW08vHde0F9uh7BjASwu6JF9JnXuEPhCY3YUqURn0+bQV/4UWeaqymmrHWV+Aw9riQCtCA==", "path": "system.security.cryptography.protecteddata/5.0.0", "hashPath": "system.security.cryptography.protecteddata.5.0.0.nupkg.sha512"}, "System.Security.Permissions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uE8juAhEkp7KDBCdjDIE3H9R1HJuEHqeqX8nLX9gmYKWwsqk3T5qZlPx8qle5DPKimC/Fy3AFTdV7HamgCh9qQ==", "path": "system.security.permissions/5.0.0", "hashPath": "system.security.permissions.5.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-6J<PERSON><PERSON>ZdaceBiLKLkYt8zJcp4xTJd1uYyXXEkPw6mnlUIjh1gZPIVKPtRXPmY5kLf6DwZmf5YLwR3QUrRonl7l0A==", "path": "system.text.encoding.codepages/4.4.0", "hashPath": "system.text.encoding.codepages.4.4.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xg4G4Indi4dqP1iuAiMSwpiWS54ZghzR644OtsRCm/m/lBMG8dUBhLVN7hLm8NNrNTR+iGbshCPTwrvxZPlm4g==", "path": "system.text.encodings.web/4.5.0", "hashPath": "system.text.encodings.web.4.5.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-WSKUTtLhPR8gllzIWO2x6l4lmAIfbyMAiTlyXAis4QBDonXK4b4S6F8zGARX4/P8wH3DH+sLdhamCiHn+fTU1A==", "path": "system.threading.tasks.extensions/4.5.1", "hashPath": "system.threading.tasks.extensions.4.5.1.nupkg.sha512"}, "System.Windows.Extensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c1ho9WU9ZxMZawML+ssPKZfdnrg/OjR3pe0m9v8230z3acqphwvPJqzAkH54xRYm5ntZHGG1EPP3sux9H3qSPg==", "path": "system.windows.extensions/5.0.0", "hashPath": "system.windows.extensions.5.0.0.nupkg.sha512"}, "Hangfire.HttpJob.Agent/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Hangfire.HttpJob.Client/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}