﻿using System;

namespace Hangfire.Heartbeat
{
    
    
    
    public sealed class HeartbeatProcessMonitorOptions
    {
        
        
        
        public TimeSpan CheckInterval { get; }

        
        
        
        
        public HeartbeatProcessMonitorOptions(TimeSpan checkInterval)
        {
            if (checkInterval == TimeSpan.Zero) throw new ArgumentException("Check interval must be nonzero value.", nameof(checkInterval));
            if (checkInterval != checkInterval.Duration()) throw new ArgumentException("Check interval must be positive value.", nameof(checkInterval));

            CheckInterval = checkInterval;
        }
    }
}
