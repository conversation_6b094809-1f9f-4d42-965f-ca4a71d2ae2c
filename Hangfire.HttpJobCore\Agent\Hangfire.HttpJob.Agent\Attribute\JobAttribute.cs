using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
namespace Hangfire.HttpJob.Agent.Attribute
{
    
    
    
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
    public class JobAttribute: System.Attribute
    {
        internal bool? enableAutoRegister;
        
        
        
        
        public bool EnableAutoRegister {
            get
            {
                if (enableAutoRegister == null) return false;
                return enableAutoRegister.Value;
            }
            set { enableAutoRegister = value; }
        }

        
        
        
        public string RegisterId { get; set; }

        
        
        
        public string RegisterName { get; set; }

    }
}