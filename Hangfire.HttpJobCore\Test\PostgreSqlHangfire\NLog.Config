<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <!-- enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Web.AspNetCore"/>
  </extensions>

  <targets>
    <target name="asyncFile" xsi:type="AsyncWrapper">
      <target name="log_file" xsi:type="File"
              fileName="${basedir}/Logs/${shortdate}/${logger}-${level}-${shortdate}.txt"
              layout="${longdate} | ${message} ${onexception:${exception:format=message} ${newline} ${stacktrace} ${newline}"
              archiveFileName="${basedir}/archives/${logger}-${level}-${shortdate}-{#####}.txt"
              archiveAboveSize="102400"
              archiveNumbering="Sequence"
              concurrentWrites="true"
              keepFileOpen="false" />


      <target xsi:type="File" name="ownFile-web" fileName="${basedir}/Logs/nlog-all-${shortdate}.log"
              layout="${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}" />

    </target>

    <target name="console" xsi:type="ColoredConsole" layout="[${date:format=HH\:mm\:ss}]:${message} ${exception:format=message}" />
  </targets>

  <rules>
    <logger name="*" minlevel="Debug" writeTo="console" />
    <logger name="Microsoft.*" minlevel="Trace" final="true" />
    <logger name="*" minlevel="Trace" writeTo="asyncFile" />
  </rules>
</nlog>