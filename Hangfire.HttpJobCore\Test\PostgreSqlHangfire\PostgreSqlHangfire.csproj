<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="NLog.Config" />
  </ItemGroup>

  <ItemGroup>
    <None Include="NLog.Config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire.PostgreSql" Version="1.8.1" />
    <PackageReference Include="Hangfire.Tags.PostgreSql" Version="1.7.1" />
    <PackageReference Include="Hangfire.Dashboard.BasicAuthorization" Version="1.0.2" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="4.9.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Server\Hangfire.HttpJob\Hangfire.HttpJob.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="hangfire\hangfire_global.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="Dockerfile">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
