﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Threading;
using Newtonsoft.Json;

namespace Hangfire.HttpJob.Agent
{
    public class ConsoleFontColor
    {
        
        public static readonly ConsoleFontColor Black = new ConsoleFontColor("#000000");
        
        public static readonly ConsoleFontColor DarkBlue = new ConsoleFontColor("#000080");
        
        public static readonly ConsoleFontColor DarkGreen = new ConsoleFontColor("#008000");
        
        public static readonly ConsoleFontColor DarkCyan = new ConsoleFontColor("#008080");
        
        public static readonly ConsoleFontColor DarkRed = new ConsoleFontColor("#800000");
        
        public static readonly ConsoleFontColor DarkMagenta = new ConsoleFontColor("#800080");
        
        public static readonly ConsoleFontColor DarkYellow = new ConsoleFontColor("#808000");
        
        public static readonly ConsoleFontColor Gray = new ConsoleFontColor("#c0c0c0");
        
        public static readonly ConsoleFontColor DarkGray = new ConsoleFontColor("#808080");
        
        public static readonly ConsoleFontColor Blue = new ConsoleFontColor("#0000ff");
        
        public static readonly ConsoleFontColor Green = new ConsoleFontColor("#00ff00");
        
        public static readonly ConsoleFontColor Cyan = new ConsoleFontColor("#00ffff");
        
        public static readonly ConsoleFontColor Red = new ConsoleFontColor("#ff0000");
        
        public static readonly ConsoleFontColor Magenta = new ConsoleFontColor("#ff00ff");
        
        public static readonly ConsoleFontColor Yellow = new ConsoleFontColor("#ffff00");
        
        public static readonly ConsoleFontColor White = new ConsoleFontColor("#ffffff");
        private readonly string _color;

        private ConsoleFontColor(string color)
        {
            this._color = color;
        }

        
        public override string ToString()
        {
            return this._color;
        }

        
        
        
        public static implicit operator string(ConsoleFontColor color)
        {
            return color?._color;
        }
    }
}
