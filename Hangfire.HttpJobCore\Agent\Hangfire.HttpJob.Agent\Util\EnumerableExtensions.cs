﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;

namespace Hangfire.HttpJob.Agent.Util
{
    
    
    
    public static class EnumerableExtensions
    {
        
        
        
        
        
        
        
        public static IEnumerable<T> WithProgress<T>(this IEnumerable<T> enumerable, IProgressBar progressBar, int count = -1)
        {
            if (enumerable is ICollection<T> collection)
            {
                count = collection.Count;
            }
            else if (enumerable is IReadOnlyCollection<T> readOnlyCollection)
            {
                count = readOnlyCollection.Count;
            }
            else if (count < 0)
            {
                throw new ArgumentException("Count is required when enumerable is not a collection", nameof(count));
            }

            return new ProgressEnumerable<T>(enumerable, progressBar, count);
        }

        
        
        
        
        
        
        public static IEnumerable WithProgress(this IEnumerable enumerable, IProgressBar progressBar, int count = -1)
        {
            if (enumerable is ICollection collection)
            {
                count = collection.Count;
            }
            else if (count < 0)
            {
                throw new ArgumentException("Count is required when enumerable is not a collection", nameof(count));
            }

            return new ProgressEnumerable(enumerable, progressBar, count);
        }

        
        
        
        
        
        
        
        
        public static IEnumerable<T> WithProgress<T>(this IEnumerable<T> enumerable, IHangfireConsole context, ConsoleFontColor color = null, int count = -1)
        {
            return WithProgress(enumerable, context.WriteProgressBar("ProgressBar", 0, color), count);
        }

        
        
        
        
        
        
        
        public static IEnumerable WithProgress(this IEnumerable enumerable, IHangfireConsole context, ConsoleFontColor color = null, int count = -1)
        {
            return WithProgress(enumerable, context.WriteProgressBar("ProgressBar", 0, color), count);
        }

        
        
        
        
        
        
        
        
        
        public static IEnumerable<T> WithProgress<T>(this IEnumerable<T> enumerable, IHangfireConsole context, string name, ConsoleFontColor color = null, int count = -1)
        {
            return WithProgress(enumerable, context.WriteProgressBar(name, 0, color), count);
        }

        
        
        
        
        
        
        
        
        public static IEnumerable WithProgress(this IEnumerable enumerable, IHangfireConsole context, string name, ConsoleFontColor color = null, int count = -1)
        {
            return WithProgress(enumerable, context.WriteProgressBar(name, 0, color), count);
        }
    }
}
