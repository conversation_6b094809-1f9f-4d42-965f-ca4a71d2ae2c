<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <Content Remove="NLog.Config" />
  </ItemGroup>

  <ItemGroup>
    <None Include="NLog.Config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="NLog.Web.AspNetCore" Version="4.9.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Agent\Hangfire.HttpJob.Agent.RedisConsole\Hangfire.HttpJob.Agent.RedisConsole.csproj" />
  </ItemGroup>
</Project>
