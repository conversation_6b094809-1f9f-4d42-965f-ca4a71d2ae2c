<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Serilog.AspNetCore" Version="2.1.1"/>
    <PackageReference Include="Serilog.Settings.Configuration" Version="3.0.1"/>
    <PackageReference Include="Serilog.Sinks.Console" Version="3.1.1"/>
    <PackageReference Include="Serilog.Sinks.File" Version="4.0.0"/>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Agent\Hangfire.HttpJob.Agent.PostgreSqlConsole\Hangfire.HttpJob.Agent.PostgreSqlConsole.csproj" />
  </ItemGroup>
</Project>
