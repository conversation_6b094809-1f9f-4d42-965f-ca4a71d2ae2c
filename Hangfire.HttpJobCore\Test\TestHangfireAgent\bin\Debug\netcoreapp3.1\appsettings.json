﻿{
  "Logging": {
    "IncludeScopes": false,
    "LogLevel": {
      "Default": "Trace",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "JobAgent": {
    "Enabled": true,
    "SitemapUrl": "/jobagent",
    "EnabledBasicAuth": true,
    "BasicUserName": "admin",
    "BasicUserPwd": "123456",
    "EnableAutoRegister": true,
    "RegisterAgentHost": "http://localhost:5002",
    "RegisterHangfireUrl": "http://localhost:5000/job",
    "RegisterHangfireBasicName": "admin",
    "RegisterHangfireBasicPwd": "test"
  }
}
