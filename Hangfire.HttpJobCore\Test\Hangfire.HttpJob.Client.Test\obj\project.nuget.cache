{"version": 2, "dgSpecHash": "JtCxTNN1Gno=", "success": true, "projectFilePath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Test\\Hangfire.HttpJob.Client.Test\\Hangfire.HttpJob.Client.Test.csproj", "expectedPackageFiles": ["D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\httpclientfactory\\1.0.3\\httpclientfactory.1.0.3.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore\\2.2.0\\microsoft.aspnetcore.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.authentication.abstractions\\2.2.0\\microsoft.aspnetcore.authentication.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.authentication.core\\2.2.0\\microsoft.aspnetcore.authentication.core.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.connections.abstractions\\2.2.0\\microsoft.aspnetcore.connections.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.diagnostics\\2.2.0\\microsoft.aspnetcore.diagnostics.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.diagnostics.abstractions\\2.2.0\\microsoft.aspnetcore.diagnostics.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.hostfiltering\\2.2.0\\microsoft.aspnetcore.hostfiltering.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.hosting\\2.2.0\\microsoft.aspnetcore.hosting.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.hosting.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.hosting.server.abstractions\\2.2.0\\microsoft.aspnetcore.hosting.server.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.http\\2.2.0\\microsoft.aspnetcore.http.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.http.extensions\\2.2.0\\microsoft.aspnetcore.http.extensions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.httpoverrides\\2.2.0\\microsoft.aspnetcore.httpoverrides.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.routing\\2.2.0\\microsoft.aspnetcore.routing.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.routing.abstractions\\2.2.0\\microsoft.aspnetcore.routing.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.server.iis\\2.2.0\\microsoft.aspnetcore.server.iis.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.server.iisintegration\\2.2.0\\microsoft.aspnetcore.server.iisintegration.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.server.kestrel\\2.2.0\\microsoft.aspnetcore.server.kestrel.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.server.kestrel.core\\2.2.0\\microsoft.aspnetcore.server.kestrel.core.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.server.kestrel.https\\2.2.0\\microsoft.aspnetcore.server.kestrel.https.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.server.kestrel.transport.abstractions\\2.2.0\\microsoft.aspnetcore.server.kestrel.transport.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.server.kestrel.transport.sockets\\2.2.0\\microsoft.aspnetcore.server.kestrel.transport.sockets.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.webutilities\\2.2.0\\microsoft.aspnetcore.webutilities.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.codecoverage\\15.9.0\\microsoft.codecoverage.15.9.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.dotnet.platformabstractions\\1.0.3\\microsoft.dotnet.platformabstractions.1.0.3.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration\\2.2.0\\microsoft.extensions.configuration.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.abstractions\\2.2.0\\microsoft.extensions.configuration.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.binder\\2.2.0\\microsoft.extensions.configuration.binder.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.commandline\\2.2.0\\microsoft.extensions.configuration.commandline.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.environmentvariables\\2.2.0\\microsoft.extensions.configuration.environmentvariables.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.fileextensions\\2.2.0\\microsoft.extensions.configuration.fileextensions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.json\\2.2.0\\microsoft.extensions.configuration.json.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.configuration.usersecrets\\2.2.0\\microsoft.extensions.configuration.usersecrets.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.dependencyinjection\\2.2.0\\microsoft.extensions.dependencyinjection.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.dependencyinjection.abstractions\\2.2.0\\microsoft.extensions.dependencyinjection.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.dependencymodel\\1.0.3\\microsoft.extensions.dependencymodel.1.0.3.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.fileproviders.abstractions\\2.2.0\\microsoft.extensions.fileproviders.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.fileproviders.physical\\2.2.0\\microsoft.extensions.fileproviders.physical.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.filesystemglobbing\\2.2.0\\microsoft.extensions.filesystemglobbing.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.hosting.abstractions\\2.2.0\\microsoft.extensions.hosting.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.logging\\2.2.0\\microsoft.extensions.logging.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.logging.abstractions\\2.2.0\\microsoft.extensions.logging.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.logging.configuration\\2.2.0\\microsoft.extensions.logging.configuration.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.logging.console\\2.2.0\\microsoft.extensions.logging.console.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.logging.debug\\2.2.0\\microsoft.extensions.logging.debug.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.logging.eventsource\\2.2.0\\microsoft.extensions.logging.eventsource.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.objectpool\\2.2.0\\microsoft.extensions.objectpool.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.options\\2.2.0\\microsoft.extensions.options.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.options.configurationextensions\\2.2.0\\microsoft.extensions.options.configurationextensions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.primitives\\2.2.0\\microsoft.extensions.primitives.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.net.http.headers\\2.2.0\\microsoft.net.http.headers.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.net.test.sdk\\15.9.0\\microsoft.net.test.sdk.15.9.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.netcore.platforms\\2.0.0\\microsoft.netcore.platforms.2.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.testplatform.objectmodel\\15.9.0\\microsoft.testplatform.objectmodel.15.9.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.testplatform.testhost\\15.9.0\\microsoft.testplatform.testhost.15.9.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.win32.registry\\4.0.0\\microsoft.win32.registry.4.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mstest.testadapter\\1.3.2\\mstest.testadapter.1.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mstest.testframework\\1.3.2\\mstest.testframework.1.3.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\newtonsoft.json\\11.0.2\\newtonsoft.json.11.0.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.buffers\\4.5.0\\system.buffers.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.collections.nongeneric\\4.0.1\\system.collections.nongeneric.4.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.collections.specialized\\4.0.1\\system.collections.specialized.4.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.componentmodel\\4.0.1\\system.componentmodel.4.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.componentmodel.annotations\\4.5.0\\system.componentmodel.annotations.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.componentmodel.eventbasedasync\\4.0.11\\system.componentmodel.eventbasedasync.4.0.11.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.componentmodel.primitives\\4.1.0\\system.componentmodel.primitives.4.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.componentmodel.typeconverter\\4.1.0\\system.componentmodel.typeconverter.4.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.diagnosticsource\\4.5.0\\system.diagnostics.diagnosticsource.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.process\\4.1.0\\system.diagnostics.process.4.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.textwritertracelistener\\4.3.0\\system.diagnostics.textwritertracelistener.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.tracesource\\4.3.0\\system.diagnostics.tracesource.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.dynamic.runtime\\4.0.11\\system.dynamic.runtime.4.0.11.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.pipelines\\4.5.2\\system.io.pipelines.4.5.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.memory\\4.5.1\\system.memory.4.5.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.net.http\\4.3.0\\system.net.http.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.private.datacontractserialization\\4.1.1\\system.private.datacontractserialization.4.1.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.metadata\\1.6.0\\system.reflection.metadata.1.6.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.typeextensions\\4.3.0\\system.reflection.typeextensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.compilerservices.unsafe\\4.5.1\\system.runtime.compilerservices.unsafe.4.5.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.loader\\4.0.0\\system.runtime.loader.4.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.serialization.json\\4.0.2\\system.runtime.serialization.json.4.0.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.serialization.primitives\\4.1.1\\system.runtime.serialization.primitives.4.1.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.cng\\4.5.0\\system.security.cryptography.cng.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.principal.windows\\4.5.0\\system.security.principal.windows.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.encodings.web\\4.5.0\\system.text.encodings.web.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.tasks.extensions\\4.5.1\\system.threading.tasks.extensions.4.5.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.thread\\4.0.0\\system.threading.thread.4.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.threadpool\\4.0.10\\system.threading.threadpool.4.0.10.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.xmldocument\\4.0.1\\system.xml.xmldocument.4.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.xmlserializer\\4.0.11\\system.xml.xmlserializer.4.0.11.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.xpath\\4.0.1\\system.xml.xpath.4.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.xpath.xmldocument\\4.0.1\\system.xml.xpath.xmldocument.4.0.1.nupkg.sha512"], "logs": []}