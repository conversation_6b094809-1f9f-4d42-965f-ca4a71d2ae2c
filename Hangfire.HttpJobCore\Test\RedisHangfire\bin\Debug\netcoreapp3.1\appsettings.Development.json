{"Logging": {"IncludeScopes": false, "LogLevel": {"Default": "Trace", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Hangfire": {"HangfireSettings": {"ServerName": "RedisHangfire", "TablePrefix": "hangfire:", "StartUpPath": "/job", "ReadOnlyPath": "", "JobQueues": ["default", "apis", "recurring"], "WorkerCount": 50, "DisplayStorageConnectionString": false, "HttpAuthInfo": {"SslRedirect": false, "RequireSsl": false, "LoginCaseSensitive": true, "IsOpenLogin": true, "Users": [{"Login": "admin", "PasswordClear": "test"}]}, "ConnectionString": "*************:6379,Password=Pmt20201023"}, "HttpJobOptions": {"Lang": "zh", "DefaultTimeZone": "", "CurrentDomain": "//", "EnableDingTalk": true, "DefaultRecurringQueueName": "recurring", "GlobalSettingJsonFilePath": "", "Proxy": "", "JobExpirationTimeoutDay": 7, "GlobalHttpTimeOut": 5000, "MailOption": {"Server": "", "Port": 0, "User": "", "Password": "", "UseSsl": false, "AlertMailList": []}, "DingTalkOption": {"Token": "", "AtPhones": "", "IsAtAll": false}, "GatewayOption": {"Enable": false, "Host": "http://*************", "Port": "10050", "AppKey": "799e6e124ad95e09b055ae8c8cc53d7f", "AppSecret": "a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0"}, "ApiRequestOption": {"Host": "*************:6379", "DB": 8, "Password": "architecture", "Timeout": "30", "IsHttps": false}, "OcelotAuthOption": {"Host": "http://*************:10065/connect/Token", "UserName": "Admin", "Password": "12345678", "GrantType": "client_credentials", "UrlPrefix": "integration-ocelot"}}}}