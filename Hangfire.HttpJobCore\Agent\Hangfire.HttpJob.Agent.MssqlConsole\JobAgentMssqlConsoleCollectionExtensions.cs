﻿using System;
using Hangfire.HttpJob.Agent.Config;
using Hangfire.HttpJob.Agent.MssqlConsole.Config;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Hangfire.HttpJob.Agent.MssqlConsole
{
    public static class JobAgentMssqlConsoleCollectionExtensions
    {
        public static IServiceCollection AddHangfireJobAgent(this IServiceCollection services, Action<JobAgentServiceConfigurer> configure = null)
        {
            services.AddHangfireHttpJobAgent(configure);
            services.AddJobAgentConsoleToSqlServer();
            return services;
        }

        public static IApplicationBuilder UseHangfireJobAgent(this IApplicationBuilder app,
            Action<JobAgentOptionsConfigurer> configureOptions = null, Action<MssqlStorageOptions> configureStorageOptions = null)
        {
            app.UseHangfireHttpJobAgent(configureOptions);
            app.UseJobAgentConsoleToSqlServer(configureStorageOptions);
            return app;
        }

        public static IServiceCollection AddJobAgentConsoleToSqlServer(this IServiceCollection serviceCollection)
        {
            serviceCollection.AddOptions();
            serviceCollection.TryAddSingleton<IConfigureOptions<MssqlStorageOptions>, MssqlConsoleOptionsConfigurer>();
            serviceCollection.TryAddSingleton<IHangfireStorage, MssqlStorage>();
            serviceCollection.TryAddTransient<IHangfireConsole, MssqlConsole>();
            serviceCollection.TryAddTransient<IStorageFactory, IMssqlStorageFactory>();
            return serviceCollection;
        }

        public static IApplicationBuilder UseJobAgentConsoleToSqlServer(this IApplicationBuilder app, Action<MssqlStorageOptions> configureOptions = null)
        {

            var sp = app.ApplicationServices;
            var evt = new EventId(1, "Hangfire.HttpJob.Agent.MssqlConsole");
            var options = sp.GetService<IOptions<MssqlStorageOptions>>();
            var loggerFactory = sp.GetService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger<MssqlConsoleOptionsConfigurer>();
            try
            {
                configureOptions?.Invoke(options.Value);
            }
            catch (Exception exception)
            {
                logger.LogCritical(evt, exception, "【Hangfire.HttpJob.Agent.MssqlConsole】 - Failed to configure Hangfire.HttpJob.Agent.MssqlConsole middleware");
            }

            JobStorageConfig.LocalJobStorageConfig = new JobStorageConfig
            {
                Type = "sqlserver",
                HangfireDb = options.Value?.HangfireDb,
                ExpireAtDays = options.Value?.ExpireAtDays
            };

            logger.LogInformation(evt, "【Hangfire.HttpJob.Agent.MssqlConsole】 - Registered MssqlConsole middleware Success!");

            return app;
        }
    }
}
