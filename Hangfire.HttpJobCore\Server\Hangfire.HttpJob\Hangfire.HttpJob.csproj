﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Content\cron.js" />
    <None Remove="Content\httpjob.js" />
    <None Remove="Content\jsoneditor.css" />
    <None Remove="Content\jsoneditor.js" />
    <None Remove="Content\sweetalert2.min.css" />
    <None Remove="Content\sweetalert2.min.js" />
    <None Remove="Dashboard\Heartbeat\Dashboard\css\styles.css" />
    <None Remove="Dashboard\Heartbeat\Dashboard\html\OverviewPage.html" />
    <None Remove="Dashboard\Heartbeat\Dashboard\js\knockout-3.4.2.js" />
    <None Remove="Dashboard\Heartbeat\Dashboard\js\knockout.bindings.orderable.js" />
    <None Remove="Dashboard\Heartbeat\Dashboard\js\numeral.min.js" />
    <None Remove="Dashboard\Heartbeat\Dashboard\js\OverviewPage.js" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Content\cron.js" />
    <EmbeddedResource Include="Content\httpjob.js" />
    <EmbeddedResource Include="Content\jsoneditor.css" />
    <EmbeddedResource Include="Content\jsoneditor.js" />
    <EmbeddedResource Include="Content\sweetalert2.min.css" />
    <EmbeddedResource Include="Content\sweetalert2.min.js" />
    <EmbeddedResource Include="Dashboard\Heartbeat\Dashboard\css\styles.css" />
    <EmbeddedResource Include="Dashboard\Heartbeat\Dashboard\html\OverviewPage.html" />
    <EmbeddedResource Include="Dashboard\Heartbeat\Dashboard\js\knockout-3.4.2.js" />
    <EmbeddedResource Include="Dashboard\Heartbeat\Dashboard\js\knockout.bindings.orderable.js" />
    <EmbeddedResource Include="Dashboard\Heartbeat\Dashboard\js\numeral.min.js" />
    <EmbeddedResource Include="Dashboard\Heartbeat\Dashboard\js\OverviewPage.js" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire.AspNetCore" Version="1.7.31" />
    <PackageReference Include="Hangfire.Console" Version="1.4.2" />
    <PackageReference Include="Hangfire.Tags" Version="1.8.3" />
    <PackageReference Include="HttpClientFactory" Version="1.0.3" />
    <PackageReference Include="MailKit" Version="2.15.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="Spring.EL" Version="1.0.5" />
    <PackageReference Include="StackExchange.Redis" Version="2.2.88" />
    <PackageReference Include="System.ComponentModel.Primitives" Version="4.3.0" />
  </ItemGroup>
</Project>
