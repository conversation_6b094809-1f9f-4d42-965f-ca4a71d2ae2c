﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddCronButtonName" xml:space="preserve">
    <value>CronGenerator</value>
  </data>
  <data name="AddHttpJobButtonName" xml:space="preserve">
    <value>AddDelayJob</value>
  </data>
  <data name="AddRecurringJobHttpJobButtonName" xml:space="preserve">
    <value>AddRecurringJob</value>
  </data>
  <data name="Choose" xml:space="preserve">
    <value>(choose one or many)</value>
  </data>
  <data name="CloseButtonName" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="CronResult" xml:space="preserve">
    <value>Cron</value>
  </data>
  <data name="DashboardName" xml:space="preserve">
    <value>JobWS</value>
  </data>
  <data name="DashboardTitle" xml:space="preserve">
    <value>JobManager</value>
  </data>
  <data name="DayA" xml:space="preserve">
    <value>Nearest weekday (Monday to Friday) to the</value>
  </data>
  <data name="DayB" xml:space="preserve">
    <value>st of the month</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Days</value>
  </data>
  <data name="Dayss" xml:space="preserve">
    <value>day(s)</value>
  </data>
  <data name="DESCRIBEEXPRESSION" xml:space="preserve">
    <value>DESCRIBE EXPRESSION</value>
  </data>
  <data name="EditRecurringJobButtonName" xml:space="preserve">
    <value>EditRecurringJob</value>
  </data>
  <data name="Every" xml:space="preserve">
    <value>Every</value>
  </data>
  <data name="EveryDay" xml:space="preserve">
    <value>Every day</value>
  </data>
  <data name="EveryDayBetweenDay" xml:space="preserve">
    <value>Every day between</value>
  </data>
  <data name="EveryHour" xml:space="preserve">
    <value>Every hour</value>
  </data>
  <data name="EveryHourBetweenHour" xml:space="preserve">
    <value>Every hour between hour</value>
  </data>
  <data name="EveryMinute" xml:space="preserve">
    <value>Every minute</value>
  </data>
  <data name="EveryMinuteBetweenMinute" xml:space="preserve">
    <value>Every minute between minute</value>
  </data>
  <data name="EveryMonth" xml:space="preserve">
    <value>Every month</value>
  </data>
  <data name="EveryMonthBetweenMonth" xml:space="preserve">
    <value>Every month between</value>
  </data>
  <data name="EveryWeekday" xml:space="preserve">
    <value>Every weekday</value>
  </data>
  <data name="EveryWeekdayBetweenWeekday" xml:space="preserve">
    <value>Every weekday between</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="Hours" xml:space="preserve">
    <value>Hours</value>
  </data>
  <data name="Hourss" xml:space="preserve">
    <value>hour(s)</value>
  </data>
  <data name="JobName" xml:space="preserve">
    <value>JobName</value>
  </data>
  <data name="JobParam" xml:space="preserve">
    <value>JobParam</value>
  </data>
  <data name="JobResult" xml:space="preserve">
    <value>JobResult</value>
  </data>
  <data name="JobStart" xml:space="preserve">
    <value>Job Start Time</value>
  </data>
  <data name="LastDayOfMonth" xml:space="preserve">
    <value>On the last day of the month</value>
  </data>
  <data name="LastWeedDayOfMonth" xml:space="preserve">
    <value>On the last weekday of the month</value>
  </data>
  <data name="LastWeekdayOfMonth" xml:space="preserve">
    <value>On the last weekday of the month</value>
  </data>
  <data name="LogOutButtonName" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="Minutes" xml:space="preserve">
    <value>Minutes</value>
  </data>
  <data name="Minutess" xml:space="preserve">
    <value>minute(s)</value>
  </data>
  <data name="Months" xml:space="preserve">
    <value>Months</value>
  </data>
  <data name="Monthss" xml:space="preserve">
    <value>month(s)</value>
  </data>
  <data name="NoSpecific" xml:space="preserve">
    <value>NoSpecific</value>
  </data>
  <data name="PauseJobButtonName" xml:space="preserve">
    <value>Pause|Start</value>
  </data>
  <data name="QueuenName" xml:space="preserve">
    <value>QueuenName</value>
  </data>
  <data name="ResponseCode" xml:space="preserve">
    <value>HttpStatusCode</value>
  </data>
  <data name="SearchPlaceholder" xml:space="preserve">
    <value>Pelese Enter JobName Or Args ...</value>
  </data>
  <data name="Specific" xml:space="preserve">
    <value>Specific</value>
  </data>
  <data name="SpecificMinute" xml:space="preserve">
    <value>Specific</value>
  </data>
  <data name="StartingAtDay" xml:space="preserve">
    <value>Starting on the</value>
  </data>
  <data name="StartingAtDay2" xml:space="preserve">
    <value>Every</value>
  </data>
  <data name="StartingAtHour" xml:space="preserve">
    <value>Starting at hour</value>
  </data>
  <data name="StartingAtHour2" xml:space="preserve">
    <value>Every</value>
  </data>
  <data name="StartingAtMinute" xml:space="preserve">
    <value>Starting at minute</value>
  </data>
  <data name="StartingAtMinute2" xml:space="preserve">
    <value>Every</value>
  </data>
  <data name="StartingAtMonth" xml:space="preserve">
    <value>Starting on the</value>
  </data>
  <data name="StartingAtMontn2" xml:space="preserve">
    <value>Every</value>
  </data>
  <data name="SubmitButtonName" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="Weekday" xml:space="preserve">
    <value>Weekday</value>
  </data>
  <data name="WeekDay1" xml:space="preserve">
    <value>Starting on the</value>
  </data>
  <data name="WeekDay12" xml:space="preserve">
    <value>st weekday  Specific day of </value>
  </data>
  <data name="JobEnd" xml:space="preserve">
    <value>JobEnd</value>
  </data>
  <data name="StartBackgroudJobButtonName" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="StopBackgroudJobButtonName" xml:space="preserve">
    <value>StopAgent</value>
  </data>
  <data name="AgentJobDeatilButton" xml:space="preserve">
    <value>AgentDetail</value>
  </data>
  <data name="MultiBackgroundJobFailToContinue" xml:space="preserve">
    <value>The ContinueJob Can‘t Start, Please Delete Or Retry this job! </value>
  </data>
  <data name="LimitReached" xml:space="preserve">
    <value>Error retry limit reached</value>
  </data>
  <data name="CallbackFail" xml:space="preserve">
    <value>Callback Fail</value>
  </data>
  <data name="CallbackStart" xml:space="preserve">
    <value>Callback Start</value>
  </data>
  <data name="CallbackSuccess" xml:space="preserve">
    <value>Callback Success</value>
  </data>
  <data name="GobalSettingButtonName" xml:space="preserve">
    <value>GlobalConfig</value>
  </data>
  <data name="ReplacePlaceHolder" xml:space="preserve">
    <value>Replace PlaceHolder</value>
  </data>
  <data name="CallbackELExcuteError" xml:space="preserve">
    <value>CallbackEL Excute Error</value>
  </data>
  <data name="CallbackELExcuteResult" xml:space="preserve">
    <value>CallbackEL Excute Result</value>
  </data>
  <data name="ExportJobsButtonName" xml:space="preserve">
    <value>ExportJobs</value>
  </data>
  <data name="ImportJobsButtonName" xml:space="preserve">
    <value>ImportJobs</value>
  </data>
  <data name="DingTalkConfig" xml:space="preserve">
    <value>HttpJob-Detail</value>
  </data>
  <data name="DingTalkLogDetail" xml:space="preserve">
    <value>JobLogDetail</value>
  </data>
  <data name="DingTalkRequestUrl" xml:space="preserve">
    <value>RequestUrl</value>
  </data>
  <data name="DingTalkResponse" xml:space="preserve">
    <value>Response</value>
  </data>
  <data name="DingTalkTitle" xml:space="preserve">
    <value>HttpJob-Alert</value>
  </data>
  <data name="AgentJobCount" xml:space="preserve">
    <value>JobCount</value>
  </data>
  <data name="AgentServer" xml:space="preserve">
    <value>Server</value>
  </data>
  <data name="AgentServerBeat" xml:space="preserve">
    <value>Heartbeat</value>
  </data>
  <data name="SearchPlaceholderUseName" xml:space="preserve">
    <value>use 【name:xxx】 to search jobDetail</value>
  </data>
</root>