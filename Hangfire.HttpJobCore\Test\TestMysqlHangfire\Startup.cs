using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using Newtonsoft.Json.Linq;
using Microsoft.Extensions.Caching.Memory;
using System.IO;
using Nacos;
using System.Linq;
using MysqlHangfire.SubscribeJob;

namespace MysqlHangfire
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            JsonConfig = configuration;
        }

        public JObject AppConfigDto
        {
            get;
            set;
        }

        public IConfiguration JsonConfig { get; }


        public void ConfigureServices(IServiceCollection services)
        {
            services.AddMemoryCache();
            var nacosConfig = JsonConfig.GetSection("Nacos");
            services.AddNacos(configure =>
            {
                configure.DefaultTimeOut = Convert.ToInt32(nacosConfig["DefaultTimeOut"]);
                configure.ServerAddresses = nacosConfig.GetSection("ServerAddresses").Get<string[]>().ToList();
                configure.Namespace = nacosConfig["Namespace"];
                configure.ListenInterval = Convert.ToInt32(nacosConfig["ListenInterval"]);
            });
            AppConfigDto = CreateRegister(services, nacosConfig);
            services.AddSelfHangfire(AppConfigDto);

            if (!string.IsNullOrEmpty(AppConfigDto?["Hangfire"]?["HttpJobOptions"]?["RabbitMQOption"]?.ToString()))
            {
                services.AddSingleton<ISubscribeJobService, SubscribeRabbitMQJobService>();
            }
        }

        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, IMemoryCache cache, ILoggerFactory logging)
        {
            var entryOptions = new MemoryCacheEntryOptions().SetPriority(CacheItemPriority.NeverRemove);
            cache.Set("AppSettings", AppConfigDto, entryOptions);
            app.ConfigureSelfHangfire(AppConfigDto);

            if (!string.IsNullOrEmpty(AppConfigDto?["Hangfire"]?["HttpJobOptions"]?["RabbitMQOption"]?.ToString()))
            {
                
                app.ApplicationServices.GetService<ISubscribeJobService>().StartAsync(AppConfigDto);   
            }
        }

        
        
        
        
        
        
        protected JObject CreateRegister(IServiceCollection services, IConfigurationSection section)
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
            if (!string.IsNullOrEmpty(environmentName) && (environmentName == "Local" || environmentName == "Development"))
            {
                string config = string.Empty;
                if (environmentName == "Development")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Development.json"));
                }
                else if (environmentName == "Local")
                {
                    config = File.ReadAllText(Path.Combine(Directory.GetCurrentDirectory(), "appsettings.Local.json"));
                }

                return JObject.Parse(config);
            }
            else
            {
                var serviceProvider = services.BuildServiceProvider();
                var configClient = serviceProvider.GetService<INacosConfigClient>();

                var res = configClient.GetConfigAsync(new GetConfigRequest
                {
                    DataId = section["DataId"],
                    Group = section["GroupId"],
                }).Result;

                if (!string.IsNullOrEmpty(res))
                {
                    var appSettings = JObject.Parse(res);
                    return appSettings;
                }

                throw new Exception("Not Found Nacos!");
            }
        }
    }
}