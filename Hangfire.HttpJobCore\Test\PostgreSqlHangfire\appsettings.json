{"Logging": {"IncludeScopes": false, "LogLevel": {"Default": "Trace", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Hangfire": {"HangfireSettings": {"ServerName": "PostgreSqlHangfire", "TablePrefix": "", "StartUpPath": "/job", "ReadOnlyPath": "", "JobQueues": ["default", "apis", "recurring"], "WorkerCount": 50, "DisplayStorageConnectionString": false, "HttpAuthInfo": {"SslRedirect": false, "RequireSsl": false, "LoginCaseSensitive": true, "IsOpenLogin": true, "Users": [{"Login": "*****", "PasswordClear": "test"}]}, "ConnectionString": "Server=***************;Port=5432;Database=hangfire;User Id=*****;Password=*****;"}, "HttpJobOptions": {"Lang": "zh", "DefaultTimeZone": "", "CurrentDomain": "//", "EnableDingTalk": true, "DefaultRecurringQueueName": "recurring", "GlobalSettingJsonFilePath": "", "Proxy": "", "JobExpirationTimeoutDay": 7, "GlobalHttpTimeOut": 5000, "MailOption": {"Server": "", "Port": 0, "User": "", "Password": "", "UseSsl": false, "AlertMailList": []}, "DingTalkOption": {"Token": "", "AtPhones": "", "IsAtAll": false}}}}