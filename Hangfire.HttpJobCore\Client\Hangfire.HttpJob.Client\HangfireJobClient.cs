﻿using HttpClientFactory.Impl;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Hangfire.HttpJob.Client
{
    public static class HangfireJobClient
    {
        
        
        
        

        internal static readonly HangfireHttpClientFactory HangfireJobHttpClientFactory = new HangfireHttpClientFactory();

        #region 添加计划任务

        
        
        
        
        
        
        
        public static Task<AddBackgroundHangfireJobResult> AddBackgroundJobAsync(string hangfireServerUrl, BackgroundJob backgroundJob, HangfireServerPostOption option = null)
        {
            return PrepareAddBackgroundHttpJobItem(hangfireServerUrl, backgroundJob, option).PostAsync<AddBackgroundHangfireJobResult>();
        }

        
        
        
        
        
        
        
        public static AddBackgroundHangfireJobResult AddBackgroundJob(string hangfireServerUrl, BackgroundJob backgroundJob, HangfireServerPostOption option = null)
        {
           
            return PrepareAddBackgroundHttpJobItem(hangfireServerUrl,backgroundJob,option).Post<AddBackgroundHangfireJobResult>();
        }

        
        
        
        
        
        
        
        public static HangfireJobResult RequeueBackgroundJob(string hangfireServerUrl, string jobId, HangfireServerPostOption option = null)
        {
            return PrepareRequeueHttpJobItem(hangfireServerUrl, jobId, option).Post<HangfireJobResult>();
        }

        
        
        
        
        
        
        
        public static Task<HangfireJobResult> RequeueBackgroundJobAsync(string hangfireServerUrl, string jobId, HangfireServerPostOption option = null)
        {
            return PrepareRequeueHttpJobItem(hangfireServerUrl, jobId, option).PostAsync<HangfireJobResult>();
        }
        
        #endregion

        #region 添加定时任务
        
        
        
        
        
        
        
        public static Task<HangfireJobResult> AddOrUpdateRecurringJobAsync(string hangfireServerUrl, RecurringJob recurringJob, HangfireServerPostOption option = null)
        {
            return PrepareAddRecurringHttpJobItem(hangfireServerUrl, recurringJob,false, option).PostAsync<HangfireJobResult>();
        }
        
        
        
        
        
        
        
        public static HangfireJobResult AddOrUpdateRecurringJob(string hangfireServerUrl, RecurringJob recurringJob, HangfireServerPostOption option = null)
        {
           
            return PrepareAddRecurringHttpJobItem(hangfireServerUrl,recurringJob,false,option).Post<HangfireJobResult>();
        }

        
        
        
        
        
        
        
        public static HangfireJobResult AddRecurringJob(string hangfireServerUrl, RecurringJob recurringJob, HangfireServerPostOption option = null)
        {

            return PrepareAddRecurringHttpJobItem(hangfireServerUrl, recurringJob,true, option).Post<HangfireJobResult>();
        }

        
        
        
        
        
        
        
        public static Task<HangfireJobResult> AddRecurringJobAsync(string hangfireServerUrl, RecurringJob recurringJob, HangfireServerPostOption option = null)
        {

            return PrepareAddRecurringHttpJobItem(hangfireServerUrl, recurringJob,true, option).PostAsync<HangfireJobResult>();
        }
        #endregion

        #region RemoveJob

        public static Task<HangfireJobResult> RemoveBackgroundJobAsync(string hangfireServerUrl, string jobId, HangfireServerPostOption option = null)
        {
            return PrepareRemoveHttpJobItem(hangfireServerUrl, jobId, true, option).PostAsync<HangfireJobResult>();
        }
        public static HangfireJobResult RemoveBackgroundJob(string hangfireServerUrl, string jobId, HangfireServerPostOption option = null)
        {
            return PrepareRemoveHttpJobItem(hangfireServerUrl, jobId, true, option).Post<HangfireJobResult>();
        }

        
        
        
        
        
        
        
        public static Task<HangfireJobResult> RemoveRecurringJobAsync(string hangfireServerUrl, string jobName, HangfireServerPostOption option = null)
        {
            return PrepareRemoveHttpJobItem(hangfireServerUrl, jobName,false, option).PostAsync<HangfireJobResult>();
        }

        public static HangfireJobResult RemoveRecurringJob(string hangfireServerUrl, string jobName, HangfireServerPostOption option = null)
        {
            return PrepareRemoveHttpJobItem(hangfireServerUrl, jobName,false, option).Post<HangfireJobResult>();
        }

        #endregion

        #region StartRecurringJob

        public static Task<HangfireJobResult> StartRecurringJobAsync(string hangfireServerUrl, string jobId, HangfireServerPostOption option = null)
        {
            return PrepareStartRecurringJobHttpJobItem(hangfireServerUrl, jobId, option).PostAsync<HangfireJobResult>();
        }
        public static HangfireJobResult StartRecurringJob(string hangfireServerUrl, string jobId, HangfireServerPostOption option = null)
        {
            return PrepareStartRecurringJobHttpJobItem(hangfireServerUrl, jobId, option).Post<HangfireJobResult>();
        }

        #endregion


        #region 构建JobItem

        
        
        
        
        
        
        
        private static HttpJobItem PrepareAddBackgroundHttpJobItem(string hangfireServerUrl, BackgroundJob backgroundJob,
            HangfireServerPostOption option = null)
        {
            if (string.IsNullOrEmpty(hangfireServerUrl))
            {
                throw new ArgumentNullException(nameof(hangfireServerUrl));
            }

            if (backgroundJob == null)
            {
                throw new ArgumentNullException(nameof(backgroundJob));
            }

            if (string.IsNullOrEmpty(backgroundJob.Url))
            {
                throw new ArgumentNullException(nameof(backgroundJob.Url));
            }

            if (string.IsNullOrEmpty(backgroundJob.JobName))
            {
                throw new ArgumentNullException(nameof(backgroundJob.JobName));
            }

            CheckChildJob(backgroundJob.Success, backgroundJob.Fail);

            if (option == null) option = new HangfireServerPostOption();
            option.HttpClient = !string.IsNullOrEmpty(option.ProxyUrl) ?
                HangfireJobHttpClientFactory.GetProxiedHttpClient(option.ProxyUrl) :
                HangfireJobHttpClientFactory.GetHttpClient(hangfireServerUrl);
            var _data = string.Empty;
            if (backgroundJob.Data != null)
            {
                if (backgroundJob.Data is string _dataStr)
                {
                    _data = _dataStr;
                }
                else
                {
                    _data = JsonConvert.SerializeObject(backgroundJob.Data);
                }
            }
            var url = hangfireServerUrl.EndsWith("/httpjob?op=backgroundjob")
                ? hangfireServerUrl
                : hangfireServerUrl + "/httpjob?op=backgroundjob";

            if (backgroundJob.RunAt != null && backgroundJob.RunAt > DateTime.Now)
            {
                backgroundJob.DelayFromMinutes = (int)(backgroundJob.RunAt.Value - DateTime.Now).TotalMinutes;
            }
            HttpJobItem jobItem = new HttpJobItem(url, option)
            {
                Url = backgroundJob.Url,
                Method = backgroundJob.Method,
                Data = _data,
                ContentType = backgroundJob.ContentType,
                Timeout = backgroundJob.Timeout,
                DelayFromMinutes = backgroundJob.DelayFromMinutes,
                JobName = backgroundJob.JobName,
                SendSuccess = backgroundJob.SendSuccess,
                SendFail = backgroundJob.SendFail,
                Mail = backgroundJob.Mail != null && backgroundJob.Mail.Any() ? string.Join(",", backgroundJob.Mail) : "",
                EnableRetry = backgroundJob.EnableRetry,
                RetryTimes = backgroundJob.RetryTimes,
                RetryDelaysInSeconds = backgroundJob.RetryDelaysInSeconds,
                BasicUserName = backgroundJob.BasicUserName,
                BasicPassword = backgroundJob.BasicPassword,
                AgentClass = backgroundJob.AgentClass,
                Headers = backgroundJob.Headers,
                CallbackEL = backgroundJob.CallbackEL,
                QueueName = backgroundJob.QueueName,
                TimeZone = backgroundJob.TimeZone,
                JobKeyWords = backgroundJob.JobKeyWords,
                QueryParams = backgroundJob.QueryParams
            };

            AppendChildJob(jobItem, backgroundJob.Success, backgroundJob.Fail);
            return jobItem;
        }


        
        
        
        
        
        
        
        
        private static HttpJobItem PrepareAddRecurringHttpJobItem(string hangfireServerUrl, RecurringJob recurringJob, bool isAddOnly = false,
            HangfireServerPostOption option = null)
        {
            if (string.IsNullOrEmpty(hangfireServerUrl))
            {
                throw new ArgumentNullException(nameof(hangfireServerUrl));
            }

            if (recurringJob == null)
            {
                throw new ArgumentNullException(nameof(recurringJob));
            }

            if (string.IsNullOrEmpty(recurringJob.JobName))
            {
                throw new ArgumentNullException(nameof(recurringJob.JobName));
            }

            CheckChildJob(recurringJob.Success, recurringJob.Fail);

            if (option == null) option = new HangfireServerPostOption();
            option.HttpClient = !string.IsNullOrEmpty(option.ProxyUrl) ?
                HangfireJobHttpClientFactory.GetProxiedHttpClient(option.ProxyUrl) :
                HangfireJobHttpClientFactory.GetHttpClient(hangfireServerUrl);
            var _data = string.Empty;
            if (recurringJob.Data != null)
            {
                if (recurringJob.Data is string _dataStr)
                {
                    _data = _dataStr;
                }
                else
                {
                    _data = JsonConvert.SerializeObject(recurringJob.Data);
                }
            }

            var url = string.Empty;
            if (isAddOnly)
            {
                url = hangfireServerUrl.EndsWith("/httpjob?op=addrecurringjob", StringComparison.OrdinalIgnoreCase)
                    ? hangfireServerUrl
                    : hangfireServerUrl + "/httpjob?op=addrecurringjob";
            }
            else
            {
                url = hangfireServerUrl.EndsWith("/httpjob?op=recurringjob", StringComparison.OrdinalIgnoreCase)
                    ? hangfireServerUrl
                    : hangfireServerUrl + "/httpjob?op=recurringjob";
            }
            HttpJobItem jobItem = new HttpJobItem(url, option)
            {
                RecurringJobIdentifier = recurringJob.RecurringJobIdentifier,
                Url = recurringJob.Url,
                Method = recurringJob.Method,
                Data = _data,
                ContentType = recurringJob.ContentType,
                Timeout = recurringJob.Timeout,
                JobName = recurringJob.JobName,
                QueueName = recurringJob.QueueName,
                Cron = recurringJob.Cron,
                SendSuccess = recurringJob.SendSuccess,
                SendFail = recurringJob.SendFail,
                Mail = recurringJob.Mail != null && recurringJob.Mail.Any() ? string.Join(",", recurringJob.Mail) : "",
                EnableRetry = recurringJob.EnableRetry,
                RetryTimes = recurringJob.RetryTimes,
                RetryDelaysInSeconds = recurringJob.RetryDelaysInSeconds,
                BasicUserName = recurringJob.BasicUserName,
                BasicPassword = recurringJob.BasicPassword,
                AgentClass = recurringJob.AgentClass,
                Headers = recurringJob.Headers,
                CallbackEL = recurringJob.CallbackEL,
                TimeZone = recurringJob.TimeZone,
                QueryParams = recurringJob.QueryParams
            };

            AppendChildJob(jobItem, recurringJob.Success, recurringJob.Fail);
            return jobItem;
        }

        
        
        
        
        
        
        
        
        private static HttpJobItem PrepareRemoveHttpJobItem(string hangfireServerUrl, string jobName, bool isBackground = false,HangfireServerPostOption option = null)
        {
            if (string.IsNullOrEmpty(hangfireServerUrl))
            {
                throw new ArgumentNullException(nameof(hangfireServerUrl));
            }
            if (string.IsNullOrEmpty(jobName))
            {
                throw new ArgumentNullException(nameof(jobName));
            }

            var recurringJob = new RecurringJob { JobName = jobName };

            if (option == null) option = new HangfireServerPostOption();
            option.HttpClient = !string.IsNullOrEmpty(option.ProxyUrl) ?
                HangfireJobHttpClientFactory.GetProxiedHttpClient(option.ProxyUrl) :
                HangfireJobHttpClientFactory.GetHttpClient(hangfireServerUrl);

            var url = hangfireServerUrl.EndsWith("/httpjob?op=deljob")
                ? hangfireServerUrl
                : hangfireServerUrl + "/httpjob?op=deljob";
            HttpJobItem jobItem = new HttpJobItem(url, option)
            {
                JobName = recurringJob.JobName,
                Url = "#",
                ContentType = "application/json",
                Data = isBackground? "backgroundjob" : ""
            };
            return jobItem;
        }

        
        
        
        
        
        
        
        private static HttpJobItem PrepareRequeueHttpJobItem(string hangfireServerUrl, string jobName, HangfireServerPostOption option = null)
        {
            if (string.IsNullOrEmpty(hangfireServerUrl))
            {
                throw new ArgumentNullException(nameof(hangfireServerUrl));
            }
            if (string.IsNullOrEmpty(jobName))
            {
                throw new ArgumentNullException(nameof(jobName));
            }

            option ??= new HangfireServerPostOption();
            option.HttpClient = !string.IsNullOrEmpty(option.ProxyUrl) ?
                HangfireJobHttpClientFactory.GetProxiedHttpClient(option.ProxyUrl) :
                HangfireJobHttpClientFactory.GetHttpClient(hangfireServerUrl);

            var url = hangfireServerUrl.EndsWith("/httpjob?op=requeuejob", StringComparison.OrdinalIgnoreCase)
                ? hangfireServerUrl
                : $"{hangfireServerUrl}/httpjob?op=requeuejob";
            HttpJobItem jobItem = new HttpJobItem(url, option)
            {
                JobName = jobName,
                Url = "#",
                ContentType = "application/json",
                Data = "requeuejob"
            };
            return jobItem;
        }

        
        
        
        
        
        
        
        private static HttpJobItem PrepareStartRecurringJobHttpJobItem(string hangfireServerUrl, string jobName, HangfireServerPostOption option = null)
        {
            if (string.IsNullOrEmpty(hangfireServerUrl))
            {
                throw new ArgumentNullException(nameof(hangfireServerUrl));
            }

            if (string.IsNullOrEmpty(jobName))
            {
                throw new ArgumentNullException(nameof(jobName));
            }

            option ??= new HangfireServerPostOption();
            option.HttpClient = !string.IsNullOrEmpty(option.ProxyUrl) ?
                HangfireJobHttpClientFactory.GetProxiedHttpClient(option.ProxyUrl) :
                HangfireJobHttpClientFactory.GetHttpClient(hangfireServerUrl);

            var url = hangfireServerUrl.EndsWith("/httpjob?op=startrecurringjob", StringComparison.OrdinalIgnoreCase)
                ? hangfireServerUrl
                : $"{hangfireServerUrl}/httpjob?op=startrecurringjob";
            HttpJobItem jobItem = new HttpJobItem(url, option)
            {
                JobName = jobName,
                Url = "#",
                ContentType = "application/json",
                Data = "recurringjobstart"
            };
            return jobItem;
        }


        #endregion

        
        
        
        
        
        
        private static void CheckChildJob(HttpCallbackJob success,HttpCallbackJob fail)
        {
            var list = new List<HttpCallbackJob>();

            void AddAllJobItem(HttpCallbackJob item, List<HttpCallbackJob> listOut)
            {
                listOut.Add(item);
                if (item.Success != null)
                {
                    AddAllJobItem(item.Success, listOut);
                }

                if (item.Fail != null)
                {
                    AddAllJobItem(item.Fail, listOut);
                }
            }

            if(success!=null)AddAllJobItem(success, list);
            if(fail!=null)AddAllJobItem(fail, list);

            foreach (var job in list)
            {
                if (string.IsNullOrEmpty(job.Url))
                {
                    throw new ArgumentNullException(nameof(HttpCallbackJob.Url));
                }
            }
        }

        
        
        
        
        
        
        private static void AppendChildJob(BaseHttpJobInfo httpJobItem,HttpCallbackJob success,HttpCallbackJob fail)
        {
            if (success != null)
            {
                string ___data;
                if (success.Data is string _dataStr)
                {
                    ___data = _dataStr;
                }
                else
                {
                    ___data = JsonConvert.SerializeObject(success.Data);
                }
                
                httpJobItem.Success = new BaseHttpJobInfo()
                {
                    Url = success.Url,
                    Method = success.Method,
                    Data = ___data,
                    ContentType = success.ContentType,
                    Timeout = success.Timeout,
                    BasicUserName = success.BasicUserName,
                    BasicPassword = success.BasicPassword,
                    AgentClass = success.AgentClass,
                    Headers = success.Headers,
                    CallbackEL = success.CallbackEL

                };

                AppendChildJob(httpJobItem.Success, success.Success, success.Fail);
            }

            if (fail != null)
            {
                string ___data;
                if (fail.Data is string _dataStr)
                {
                    ___data = _dataStr;
                }
                else
                {
                    ___data = JsonConvert.SerializeObject(fail.Data);
                }
                
                httpJobItem.Fail = new BaseHttpJobInfo()
                {
                    Url = fail.Url,
                    Method = fail.Method,
                    Data = ___data,
                    ContentType = fail.ContentType,
                    Timeout = fail.Timeout,
                    BasicUserName = fail.BasicUserName,
                    BasicPassword = fail.BasicPassword,
                    AgentClass = fail.AgentClass,
                    Headers = fail.Headers,
                    CallbackEL = fail.CallbackEL
                };

                AppendChildJob(httpJobItem.Fail, fail.Success, fail.Fail);
            }
        }
    }
}
