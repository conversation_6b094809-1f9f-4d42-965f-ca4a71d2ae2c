﻿using System;
using System.Collections.Generic;
using System.Text;
using Hangfire.Common;
using Hangfire.States;
using Newtonsoft.Json;

namespace Hangfire.HttpJob.Support
{
    public class ProcessState : IState
    {
        
        
        
        
        
        
        public static readonly string StateName = "Processing";

        internal ProcessState(string serverId, string workerId,DateTime startAt)
        {
            if (string.IsNullOrWhiteSpace(serverId))
                throw new ArgumentNullException(nameof(serverId));
            this.ServerId = serverId;
            this.StartedAt = startAt;
            this.WorkerId = workerId;
        }

        
        
        
        [JsonIgnore]
        public DateTime StartedAt { get; }

        
        
        
        
        
        
        public string ServerId { get; }

        
        
        
        
        public string WorkerId { get; }

        
        
        
        
        
        
        [JsonIgnore]
        public string Name
        {
            get
            {
                return StateName;
            }
        }

        
        public string Reason { get; set; }

        
        
        
        
        
        
        [JsonIgnore]
        public bool IsFinal
        {
            get
            {
                return false;
            }
        }

        
        
        
        
        
        
        
        [JsonIgnore]
        public bool IgnoreJobLoadException
        {
            get
            {
                return false;
            }
        }

        
        
        
        
        
        
        ///     <listheader>
        ///         <term>Key</term>
        ///         <term>Type</term>
        ///         <term>Deserialize Method</term>
        ///         <description>Notes</description>
        ///     </listheader>
        ///     <item>
        ///         <term><c>StartedAt</c></term>
        ///         <term><see cref="T:System.DateTime" /></term>
        ///         <term><see cref="M:Hangfire.Common.JobHelper.DeserializeDateTime(System.String)" /></term>
        ///         <description>Please see the <see cref="P:Hangfire.States.ProcessingState.StartedAt" /> property.</description>
        ///     </item>
        ///     <item>
        ///         <term><c>ServerId</c></term>
        ///         <term><see cref="T:System.String" /></term>
        ///         <term><i>Not required</i></term>
        ///         <description>Please see the <see cref="P:Hangfire.States.ProcessingState.ServerId" /> property.</description>
        ///     </item>
        ///     <item>
        ///         <term><c>WorkerId</c></term>
        ///         <term><see cref="T:System.String" /></term>
        ///         <term><i>Not required</i></term>
        ///         <description>Please see the <see cref="P:Hangfire.States.ProcessingState.WorkerId" /> property.</description>
        ///     </item>
        
        
        public Dictionary<string, string> SerializeData()
        {
            return new Dictionary<string, string>()
      {
        {
          "StartedAt",
          JobHelper.SerializeDateTime(this.StartedAt)
        },
        {
          "ServerId",
          this.ServerId
        },
        {
          "WorkerId",
          this.WorkerId
        }
      };
        }
    }
}
