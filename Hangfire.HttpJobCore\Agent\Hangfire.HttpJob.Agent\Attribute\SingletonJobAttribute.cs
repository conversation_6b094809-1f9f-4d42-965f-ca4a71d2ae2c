﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Hangfire.HttpJob.Agent.Attribute
{
    
    
    
    [AttributeUsage(AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
    public class SingletonJobAttribute : JobAttribute
    {
        public SingletonJobAttribute()
        {
            
        }
        public SingletonJobAttribute(string registerId)
        {
            this.RegisterId = registerId;
        }
    }
}
