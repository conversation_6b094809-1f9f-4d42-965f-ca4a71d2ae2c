{
    "Logging": {
        "LogLevel": {
            "Default": "Debug",
            "System": "Information",
            "Microsoft": "Information"
        }
    },
    "Hangfire": {
        "HangfireSettings": {
            "ServerName": "MysqlHangfire",
            "TablePrefix": "hangfire",
            "StartUpPath": "/job",
            "ReadOnlyPath": "",
            "JobQueues": [ "default", "apis", "recurring", "endpoint.instance_start", "endpoint.activity_turning", "endpoint.activity_completed", "endpoint.instance_end", "tripartite_system.instance_start", "tripartite_system.activity_turning", "tripartite_system.activity_completed", "tripartite_system.instance_end" ],
            "WorkerCount": 50,
            "DisplayStorageConnectionString": false,
            "HttpAuthInfo": {
                "SslRedirect": false,
                "RequireSsl": false,
                "LoginCaseSensitive": true,
                "IsOpenLogin": true,
                "Users": [
                    {
                        "Login": "admin",
                        "PasswordClear": "test"
                    }
                ]
            },
            "ConnectionString": "Server=*************;Port=3306;Database=hangfire;Uid=root;Pwd=*******;charset=utf8;SslMode=none;Allow User Variables=True"
        },
        "HttpJobOptions": {
            "Lang": "zh",
            "DefaultTimeZone": "Asia/Shanghai",
            "CurrentDomain": "//",
            "EnableDingTalk": true,
            "DefaultRecurringQueueName": "recurring",
            "GlobalSettingJsonFilePath": "",
            "Proxy": "",
            "JobExpirationTimeoutDay": 7,
            "GlobalHttpTimeOut": 5000,
            "MailOption": {
                "Server": "",
                "Port": 0,
                "User": "",
                "Password": "",
                "UseSsl": false,
                "AlertMailList": []
            },
            "DingTalkOption": {
                "Token": "",
                "AtPhones": "",
                "IsAtAll": false
            },
            "GatewayOption": {
                "Enable": false,
                "Host": "http://*************",
                "Port": "10050",
                "AppKey": "799e6e124ad95e09b055ae8c8cc53d7f",
                "AppSecret": "a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0"
            },
            "ApiRequestOption": {
                "Host": "*************:6379",
                "DB": 8,
                "Password": "architecture",
                "Timeout": "30",
                "IsHttps": false
            },
            "OcelotAuthOption": {
                "Host": "http://*************:10065/connect/Token",
                "UserName": "Admin",
                "Password": "12345678",
                "GrantType": "client_credentials",
                "UrlPrefix": "integration-ocelot"
            }
            /*"RabbitMQOption": [
                {
                    "HostName": "**************",
                    "Port": 5672,
                    "UserName": "admin",
                    "Password": "admin",
                    "VirtualHost": "/",
                    "Queues": [
                        {
                            "Name": "TestHangfire",
                            "NumOfThreads": 3
                            //"UrlReplaces": [
                            //    {
                            //        "OldStr": "",
                            //        "NewStr": ""
                            //    }
                            //],
                            //"MatchQueueByUrls": [
                            //    {
                            //        "UrlKey": "",
                            //        "JobQueues": []
                            //    }
                            //]
                        }
                    ]
                }
            ]*/
        }
    }
}
