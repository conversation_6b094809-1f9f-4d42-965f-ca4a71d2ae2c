﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Hangfire.HttpJob.Agent.Config
{
    
    
    
    public class JobAgentOptions
    {
        
        
        
        public bool Enabled { get; set; } = true;
        
        
        
        
        public string SitemapUrl { get; set; } = "/jobagent";

        
        
        
        public bool EnabledBasicAuth { get; set; }
        
        
        
        
        public string BasicUserName { get; set; } 
        
        
        
        
        public string BasicUserPwd { get; set; } 
        
        
        
        
        public bool EnableAutoRegister { get; set; }
        
        
        
        
        public string RegisterHangfireUrl { get; set; }
        
        
        
        public string RegisterAgentHost { get; set; }
        
        
        
        
        public string RegisterHangfireBasicName { get; set; } 
        
        
        
        
        public string RegisterHangfireBasicPwd { get; set; } 
    }
}
