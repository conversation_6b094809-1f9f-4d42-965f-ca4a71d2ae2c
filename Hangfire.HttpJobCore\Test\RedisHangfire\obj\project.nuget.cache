{"version": 2, "dgSpecHash": "Risclq82VXg=", "success": true, "projectFilePath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Test\\RedisHangfire\\RedisHangfire.csproj", "expectedPackageFiles": ["D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\hangfire.aspnetcore\\1.7.31\\hangfire.aspnetcore.1.7.31.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\hangfire.console\\1.4.2\\hangfire.console.1.4.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\hangfire.core\\1.7.31\\hangfire.core.1.7.31.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\hangfire.dashboard.basicauthorization\\1.0.2\\hangfire.dashboard.basicauthorization.1.0.2.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\hangfire.redis.stackexchange\\1.8.1\\hangfire.redis.stackexchange.1.8.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\hangfire.tags\\1.8.3\\hangfire.tags.1.8.3.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\hangfire.tags.redis.stackexchange\\1.8.0\\hangfire.tags.redis.stackexchange.1.8.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\httpclientfactory\\1.0.3\\httpclientfactory.1.0.3.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mailkit\\2.15.0\\mailkit.2.15.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.http.abstractions\\2.2.0\\microsoft.aspnetcore.http.abstractions.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.aspnetcore.http.features\\2.2.0\\microsoft.aspnetcore.http.features.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.extensions.primitives\\2.2.0\\microsoft.extensions.primitives.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.win32.primitives\\4.3.0\\microsoft.win32.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\microsoft.win32.systemevents\\5.0.0\\microsoft.win32.systemevents.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\mimekit\\2.15.0\\mimekit.2.15.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\netstandard.library\\1.6.1\\netstandard.library.1.6.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\pipelines.sockets.unofficial\\2.2.0\\pipelines.sockets.unofficial.2.2.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\portable.bouncycastle\\1.8.10\\portable.bouncycastle.1.8.10.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system\\4.3.0\\runtime.native.system.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.io.compression\\4.3.0\\runtime.native.system.io.compression.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.net.http\\4.3.0\\runtime.native.system.net.http.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl\\4.3.0\\runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\spring.el\\1.0.5\\spring.el.1.0.5.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\stackexchange.redis\\2.2.88\\stackexchange.redis.2.2.88.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.appcontext\\4.3.0\\system.appcontext.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.codedom\\4.5.0\\system.codedom.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.collections\\4.3.0\\system.collections.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.collections.concurrent\\4.3.0\\system.collections.concurrent.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.componentmodel\\4.3.0\\system.componentmodel.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.componentmodel.primitives\\4.3.0\\system.componentmodel.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.configuration.configurationmanager\\5.0.0\\system.configuration.configurationmanager.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.console\\4.3.0\\system.console.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.debug\\4.3.0\\system.diagnostics.debug.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.diagnosticsource\\4.3.0\\system.diagnostics.diagnosticsource.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.performancecounter\\5.0.0\\system.diagnostics.performancecounter.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.tools\\4.3.0\\system.diagnostics.tools.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.diagnostics.tracing\\4.3.0\\system.diagnostics.tracing.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.drawing.common\\5.0.0\\system.drawing.common.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.globalization\\4.3.0\\system.globalization.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.globalization.calendars\\4.3.0\\system.globalization.calendars.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.globalization.extensions\\4.3.0\\system.globalization.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.compression\\4.3.0\\system.io.compression.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.compression.zipfile\\4.3.0\\system.io.compression.zipfile.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.filesystem\\4.3.0\\system.io.filesystem.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.filesystem.primitives\\4.3.0\\system.io.filesystem.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.io.pipelines\\5.0.0\\system.io.pipelines.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.linq\\4.3.0\\system.linq.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.linq.expressions\\4.3.0\\system.linq.expressions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.memory\\4.5.1\\system.memory.4.5.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.net.http\\4.3.0\\system.net.http.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.net.primitives\\4.3.0\\system.net.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.net.sockets\\4.3.0\\system.net.sockets.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.objectmodel\\4.3.0\\system.objectmodel.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.emit\\4.3.0\\system.reflection.emit.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.emit.ilgeneration\\4.3.0\\system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.emit.lightweight\\4.3.0\\system.reflection.emit.lightweight.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.extensions\\4.3.0\\system.reflection.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.reflection.typeextensions\\4.4.0\\system.reflection.typeextensions.4.4.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.resources.resourcemanager\\4.3.0\\system.resources.resourcemanager.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.compilerservices.unsafe\\4.5.1\\system.runtime.compilerservices.unsafe.4.5.1.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.extensions\\4.3.0\\system.runtime.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.handles\\4.3.0\\system.runtime.handles.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.interopservices\\4.3.0\\system.runtime.interopservices.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.interopservices.runtimeinformation\\4.3.0\\system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.runtime.numerics\\4.3.0\\system.runtime.numerics.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.algorithms\\4.3.0\\system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.cng\\4.7.0\\system.security.cryptography.cng.4.7.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.csp\\4.3.0\\system.security.cryptography.csp.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.encoding\\4.3.0\\system.security.cryptography.encoding.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.openssl\\4.3.0\\system.security.cryptography.openssl.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.pkcs\\4.7.0\\system.security.cryptography.pkcs.4.7.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.primitives\\4.3.0\\system.security.cryptography.primitives.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.protecteddata\\5.0.0\\system.security.cryptography.protecteddata.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.cryptography.x509certificates\\4.3.0\\system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.permissions\\5.0.0\\system.security.permissions.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.encoding.codepages\\4.4.0\\system.text.encoding.codepages.4.4.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.encoding.extensions\\4.3.0\\system.text.encoding.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.encodings.web\\4.5.0\\system.text.encodings.web.4.5.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.text.regularexpressions\\4.3.0\\system.text.regularexpressions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading\\4.3.0\\system.threading.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.tasks.extensions\\4.3.0\\system.threading.tasks.extensions.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.threading.timer\\4.3.0\\system.threading.timer.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.windows.extensions\\5.0.0\\system.windows.extensions.5.0.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.readerwriter\\4.3.0\\system.xml.readerwriter.4.3.0.nupkg.sha512", "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\system.xml.xdocument\\4.3.0\\system.xml.xdocument.4.3.0.nupkg.sha512"], "logs": []}