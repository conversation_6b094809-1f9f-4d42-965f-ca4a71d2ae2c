{"format": 1, "restore": {"E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Test\\PostgreSqlHangfire\\PostgreSqlHangfire.csproj": {}}, "projects": {"E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Server\\Hangfire.HttpJob\\Hangfire.HttpJob.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Server\\Hangfire.HttpJob\\Hangfire.HttpJob.csproj", "projectName": "Hangfire.HttpJob", "projectPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Server\\Hangfire.HttpJob\\Hangfire.HttpJob.csproj", "packagesPath": "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\", "outputPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Server\\Hangfire.HttpJob\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://172.19.50.115:15840/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Hangfire.AspNetCore": {"target": "Package", "version": "[1.7.31, )"}, "Hangfire.Console": {"target": "Package", "version": "[1.4.2, )"}, "Hangfire.Tags": {"target": "Package", "version": "[1.8.3, )"}, "HttpClientFactory": {"target": "Package", "version": "[1.0.3, )"}, "MailKit": {"target": "Package", "version": "[2.15.0, )"}, "Microsoft.AspNetCore.Http.Abstractions": {"target": "Package", "version": "[2.2.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.1, )"}, "Spring.EL": {"target": "Package", "version": "[1.0.5, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.2.88, )"}, "System.ComponentModel.Primitives": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Test\\PostgreSqlHangfire\\PostgreSqlHangfire.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Test\\PostgreSqlHangfire\\PostgreSqlHangfire.csproj", "projectName": "PostgreSqlHangfire", "projectPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Test\\PostgreSqlHangfire\\PostgreSqlHangfire.csproj", "packagesPath": "D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\", "outputPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Test\\PostgreSqlHangfire\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config", "C:\\Program Files (x86)\\NuGet\\Config\\Xamarin.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"D:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "http://172.19.50.115:15840/nuget": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Server\\Hangfire.HttpJob\\Hangfire.HttpJob.csproj": {"projectPath": "E:\\03Work\\07Movitech\\03Project\\ShuiWu_BPM\\ShuiWu_BPM_SourceCode_20250804\\Hangfire.HttpJobCore\\Server\\Hangfire.HttpJob\\Hangfire.HttpJob.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Hangfire.Dashboard.BasicAuthorization": {"target": "Package", "version": "[1.0.2, )"}, "Hangfire.PostgreSql": {"target": "Package", "version": "[1.8.1, )"}, "Hangfire.Tags.PostgreSql": {"target": "Package", "version": "[1.7.1, )"}, "NLog.Web.AspNetCore": {"target": "Package", "version": "[4.9.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}