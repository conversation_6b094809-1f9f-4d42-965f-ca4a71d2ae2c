﻿using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using HttpClientFactory.Impl;
using Newtonsoft.Json;

namespace Hangfire.HttpJob.Server
{
    public class HttpJobItem : BaseJobItems
    {
        public HttpJobItem()
        {
            Method = "Post";
            ContentType = "application/json";
            DelayFromMinutes = 15;
            AllowMultiple = true;
        }

        
        
        
        [JsonIgnore]
        public string CallbackRoot { get; set; }

        
        
        
        public HttpJobItem Success { get; set; }

        
        
        
        public HttpJobItem Fail { get; set; }

        
        
        
        public string JobId { get; set; }

        
        
        
        public string JobKeyWords { get; set; }

        
        
        
        public bool AllowMultiple { get; set; }

        public override string ToString()
        {
            return JsonConvert.SerializeObject(this);
        }

        public string GetUrlHost()
        {
            var uri = new Uri(Url);
            var key = uri.Host + ":" + uri.Port;
            return key;
        }
    }

    [JsonObject(ItemNullValueHandling = NullValueHandling.Ignore)]
    public class RecurringJobItem : BaseJobItems
    {
        
        
        
        public RecurringJobChildItem Success { get; set; }

        
        
        
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)]
        public RecurringJobChildItem Fail { get; set; }

        
        
        
        public bool AllowMultiple { get; set; }
    }

    [JsonObject(ItemNullValueHandling = NullValueHandling.Ignore)]
    public class RecurringJobChildItem
    { 
        
        
        
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)] 
        public string Url { get; set; }

        
        
        
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)] 
        public string Method { get; set; }

        
        
        
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)] 
        public string Data { get; set; }

        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)] 
        public string ContentType { get; set; }

        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)] 
        public int Timeout { get; set; }

        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)] 
        public string QueueName { get; set; }

        
        
        
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)] 
        public string AgentClass { get; set; }

        
        
        
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)] 
        public Dictionary<string, string> Headers { get; set; }

        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)] 
        public string BasicUserName { get; set; }

        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Ignore)] 
        public string BasicPassword { get; set; }

    }


    public class BaseJobItems
    {
        
        
        
        public string RecurringJobIdentifier { get; set; }

        
        
        
        public string Url { get; set; }

        
        
        
        public string Method { get; set; }

        
        
        
        public string Data { get; set; }

        
        
        
        public string ContentType { get; set; }

        
        
        
        public int Timeout { get; set; }

        
        
        
        public int DelayFromMinutes { get; set; }

        
        
        
        public string RunAt { get; set; }

        
        
        
        public string Cron { get; set; }
        
        
        
        public string JobName { get; set; }
        
        
        
        public string QueueName { get; set; }

        
        
        
        public string AgentClass { get; set; }

        
        
        
        public int AgentTimeout { get; set; }

        
        
        
        public bool SendSuccess { get; set; }

        
        
        
        public bool SendFail { get; set; }

        
        
        
        public string Mail { get; set; }

        
        
        
        public bool EnableRetry { get; set; }

        
        
        
        public string RetryDelaysInSeconds { get; set; }

        
        
        
        public int RetryTimes { get; set; }

        
        
        
        public string BasicUserName { get; set; }
        
        
        
        public string BasicPassword { get; set; }

        
        
        
        public Dictionary<string, string> Headers { get; set; }

        
        
        
        public Dictionary<string, object> QueryParams { get; set; }

        
        
        
        public string CallbackEL { get; set; }

        
        
        
        public string TimeZone { get; set; }

        
        
        
        public DingTalkOption DingTalk { get; set; } 
    }
}
