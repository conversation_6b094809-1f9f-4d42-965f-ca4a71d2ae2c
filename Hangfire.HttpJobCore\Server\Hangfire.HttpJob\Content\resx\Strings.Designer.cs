﻿//------------------------------------------------------------------------------

//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。

//------------------------------------------------------------------------------

namespace Hangfire.HttpJob.Content.resx {
    using System;
    
    
    
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    
    
    
    
    
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Strings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Strings() {
        }
        
        
        ///   返回此类使用的缓存的 ResourceManager 实例。
        
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Hangfire.HttpJob.Content.resx.Strings", typeof(Strings).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        
        ///   查找类似 CronGenerator 的本地化字符串。
        
        public static string AddCronButtonName {
            get {
                return ResourceManager.GetString("AddCronButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 AddDelayJob 的本地化字符串。
        
        public static string AddHttpJobButtonName {
            get {
                return ResourceManager.GetString("AddHttpJobButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 AddRecurringJob 的本地化字符串。
        
        public static string AddRecurringJobHttpJobButtonName {
            get {
                return ResourceManager.GetString("AddRecurringJobHttpJobButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 JobCount 的本地化字符串。
        
        public static string AgentJobCount {
            get {
                return ResourceManager.GetString("AgentJobCount", resourceCulture);
            }
        }
        
        
        ///   查找类似 AgentDetail 的本地化字符串。
        
        public static string AgentJobDeatilButton {
            get {
                return ResourceManager.GetString("AgentJobDeatilButton", resourceCulture);
            }
        }
        
        
        ///   查找类似 Server 的本地化字符串。
        
        public static string AgentServer {
            get {
                return ResourceManager.GetString("AgentServer", resourceCulture);
            }
        }
        
        
        ///   查找类似 Heartbeat 的本地化字符串。
        
        public static string AgentServerBeat {
            get {
                return ResourceManager.GetString("AgentServerBeat", resourceCulture);
            }
        }
        
        
        ///   查找类似 CallbackEL Excute Error 的本地化字符串。
        
        public static string CallbackELExcuteError {
            get {
                return ResourceManager.GetString("CallbackELExcuteError", resourceCulture);
            }
        }
        
        
        ///   查找类似 CallbackEL Excute Result 的本地化字符串。
        
        public static string CallbackELExcuteResult {
            get {
                return ResourceManager.GetString("CallbackELExcuteResult", resourceCulture);
            }
        }
        
        
        ///   查找类似 Callback Fail 的本地化字符串。
        
        public static string CallbackFail {
            get {
                return ResourceManager.GetString("CallbackFail", resourceCulture);
            }
        }
        
        
        ///   查找类似 Callback Start 的本地化字符串。
        
        public static string CallbackStart {
            get {
                return ResourceManager.GetString("CallbackStart", resourceCulture);
            }
        }
        
        
        ///   查找类似 Callback Success 的本地化字符串。
        
        public static string CallbackSuccess {
            get {
                return ResourceManager.GetString("CallbackSuccess", resourceCulture);
            }
        }
        
        
        ///   查找类似 (choose one or many) 的本地化字符串。
        
        public static string Choose {
            get {
                return ResourceManager.GetString("Choose", resourceCulture);
            }
        }
        
        
        ///   查找类似 Close 的本地化字符串。
        
        public static string CloseButtonName {
            get {
                return ResourceManager.GetString("CloseButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 Cron 的本地化字符串。
        
        public static string CronResult {
            get {
                return ResourceManager.GetString("CronResult", resourceCulture);
            }
        }
        
        
        ///   查找类似 JobWS 的本地化字符串。
        
        public static string DashboardName {
            get {
                return ResourceManager.GetString("DashboardName", resourceCulture);
            }
        }
        
        
        ///   查找类似 JobManager 的本地化字符串。
        
        public static string DashboardTitle {
            get {
                return ResourceManager.GetString("DashboardTitle", resourceCulture);
            }
        }
        
        
        ///   查找类似 Nearest weekday (Monday to Friday) to the 的本地化字符串。
        
        public static string DayA {
            get {
                return ResourceManager.GetString("DayA", resourceCulture);
            }
        }
        
        
        ///   查找类似 st of the month 的本地化字符串。
        
        public static string DayB {
            get {
                return ResourceManager.GetString("DayB", resourceCulture);
            }
        }
        
        
        ///   查找类似 Days 的本地化字符串。
        
        public static string Days {
            get {
                return ResourceManager.GetString("Days", resourceCulture);
            }
        }
        
        
        ///   查找类似 day(s) 的本地化字符串。
        
        public static string Dayss {
            get {
                return ResourceManager.GetString("Dayss", resourceCulture);
            }
        }
        
        
        ///   查找类似 DESCRIBE EXPRESSION 的本地化字符串。
        
        public static string DESCRIBEEXPRESSION {
            get {
                return ResourceManager.GetString("DESCRIBEEXPRESSION", resourceCulture);
            }
        }
        
        
        ///   查找类似 HttpJob-Detail 的本地化字符串。
        
        public static string DingTalkConfig {
            get {
                return ResourceManager.GetString("DingTalkConfig", resourceCulture);
            }
        }
        
        
        ///   查找类似 JobLogDetail 的本地化字符串。
        
        public static string DingTalkLogDetail {
            get {
                return ResourceManager.GetString("DingTalkLogDetail", resourceCulture);
            }
        }
        
        
        ///   查找类似 RequestUrl 的本地化字符串。
        
        public static string DingTalkRequestUrl {
            get {
                return ResourceManager.GetString("DingTalkRequestUrl", resourceCulture);
            }
        }
        
        
        ///   查找类似 Response 的本地化字符串。
        
        public static string DingTalkResponse {
            get {
                return ResourceManager.GetString("DingTalkResponse", resourceCulture);
            }
        }
        
        
        ///   查找类似 HttpJob-Alert 的本地化字符串。
        
        public static string DingTalkTitle {
            get {
                return ResourceManager.GetString("DingTalkTitle", resourceCulture);
            }
        }
        
        
        ///   查找类似 EditRecurringJob 的本地化字符串。
        
        public static string EditRecurringJobButtonName {
            get {
                return ResourceManager.GetString("EditRecurringJobButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every 的本地化字符串。
        
        public static string Every {
            get {
                return ResourceManager.GetString("Every", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every day 的本地化字符串。
        
        public static string EveryDay {
            get {
                return ResourceManager.GetString("EveryDay", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every day between 的本地化字符串。
        
        public static string EveryDayBetweenDay {
            get {
                return ResourceManager.GetString("EveryDayBetweenDay", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every hour 的本地化字符串。
        
        public static string EveryHour {
            get {
                return ResourceManager.GetString("EveryHour", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every hour between hour 的本地化字符串。
        
        public static string EveryHourBetweenHour {
            get {
                return ResourceManager.GetString("EveryHourBetweenHour", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every minute 的本地化字符串。
        
        public static string EveryMinute {
            get {
                return ResourceManager.GetString("EveryMinute", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every minute between minute 的本地化字符串。
        
        public static string EveryMinuteBetweenMinute {
            get {
                return ResourceManager.GetString("EveryMinuteBetweenMinute", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every month 的本地化字符串。
        
        public static string EveryMonth {
            get {
                return ResourceManager.GetString("EveryMonth", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every month between 的本地化字符串。
        
        public static string EveryMonthBetweenMonth {
            get {
                return ResourceManager.GetString("EveryMonthBetweenMonth", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every weekday 的本地化字符串。
        
        public static string EveryWeekday {
            get {
                return ResourceManager.GetString("EveryWeekday", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every weekday between 的本地化字符串。
        
        public static string EveryWeekdayBetweenWeekday {
            get {
                return ResourceManager.GetString("EveryWeekdayBetweenWeekday", resourceCulture);
            }
        }
        
        
        ///   查找类似 ExportJobs 的本地化字符串。
        
        public static string ExportJobsButtonName {
            get {
                return ResourceManager.GetString("ExportJobsButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 From 的本地化字符串。
        
        public static string From {
            get {
                return ResourceManager.GetString("From", resourceCulture);
            }
        }
        
        
        ///   查找类似 GlobalConfig 的本地化字符串。
        
        public static string GobalSettingButtonName {
            get {
                return ResourceManager.GetString("GobalSettingButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 Hours 的本地化字符串。
        
        public static string Hours {
            get {
                return ResourceManager.GetString("Hours", resourceCulture);
            }
        }
        
        
        ///   查找类似 hour(s) 的本地化字符串。
        
        public static string Hourss {
            get {
                return ResourceManager.GetString("Hourss", resourceCulture);
            }
        }
        
        
        ///   查找类似 ImportJobs 的本地化字符串。
        
        public static string ImportJobsButtonName {
            get {
                return ResourceManager.GetString("ImportJobsButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 JobEnd 的本地化字符串。
        
        public static string JobEnd {
            get {
                return ResourceManager.GetString("JobEnd", resourceCulture);
            }
        }
        
        
        ///   查找类似 JobName 的本地化字符串。
        
        public static string JobName {
            get {
                return ResourceManager.GetString("JobName", resourceCulture);
            }
        }
        
        
        ///   查找类似 JobParam 的本地化字符串。
        
        public static string JobParam {
            get {
                return ResourceManager.GetString("JobParam", resourceCulture);
            }
        }
        
        
        ///   查找类似 JobResult 的本地化字符串。
        
        public static string JobResult {
            get {
                return ResourceManager.GetString("JobResult", resourceCulture);
            }
        }
        
        
        ///   查找类似 Job Start Time 的本地化字符串。
        
        public static string JobStart {
            get {
                return ResourceManager.GetString("JobStart", resourceCulture);
            }
        }
        
        
        ///   查找类似 On the last day of the month 的本地化字符串。
        
        public static string LastDayOfMonth {
            get {
                return ResourceManager.GetString("LastDayOfMonth", resourceCulture);
            }
        }
        
        
        ///   查找类似 On the last weekday of the month 的本地化字符串。
        
        public static string LastWeedDayOfMonth {
            get {
                return ResourceManager.GetString("LastWeedDayOfMonth", resourceCulture);
            }
        }
        
        
        ///   查找类似 On the last weekday of the month 的本地化字符串。
        
        public static string LastWeekdayOfMonth {
            get {
                return ResourceManager.GetString("LastWeekdayOfMonth", resourceCulture);
            }
        }
        
        
        ///   查找类似 Error retry limit reached 的本地化字符串。
        
        public static string LimitReached {
            get {
                return ResourceManager.GetString("LimitReached", resourceCulture);
            }
        }
        
        
        ///   查找类似 Back 的本地化字符串。
        
        public static string LogOutButtonName {
            get {
                return ResourceManager.GetString("LogOutButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 Minutes 的本地化字符串。
        
        public static string Minutes {
            get {
                return ResourceManager.GetString("Minutes", resourceCulture);
            }
        }
        
        
        ///   查找类似 minute(s) 的本地化字符串。
        
        public static string Minutess {
            get {
                return ResourceManager.GetString("Minutess", resourceCulture);
            }
        }
        
        
        ///   查找类似 Months 的本地化字符串。
        
        public static string Months {
            get {
                return ResourceManager.GetString("Months", resourceCulture);
            }
        }
        
        
        ///   查找类似 month(s) 的本地化字符串。
        
        public static string Monthss {
            get {
                return ResourceManager.GetString("Monthss", resourceCulture);
            }
        }
        
        
        ///   查找类似 The ContinueJob Can‘t Start, Please Delete Or Retry this job!  的本地化字符串。
        
        public static string MultiBackgroundJobFailToContinue {
            get {
                return ResourceManager.GetString("MultiBackgroundJobFailToContinue", resourceCulture);
            }
        }
        
        
        ///   查找类似 NoSpecific 的本地化字符串。
        
        public static string NoSpecific {
            get {
                return ResourceManager.GetString("NoSpecific", resourceCulture);
            }
        }
        
        
        ///   查找类似 Pause|Start 的本地化字符串。
        
        public static string PauseJobButtonName {
            get {
                return ResourceManager.GetString("PauseJobButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 QueuenName 的本地化字符串。
        
        public static string QueuenName {
            get {
                return ResourceManager.GetString("QueuenName", resourceCulture);
            }
        }
        
        
        ///   查找类似 Replace PlaceHolder 的本地化字符串。
        
        public static string ReplacePlaceHolder {
            get {
                return ResourceManager.GetString("ReplacePlaceHolder", resourceCulture);
            }
        }
        
        
        ///   查找类似 HttpStatusCode 的本地化字符串。
        
        public static string ResponseCode {
            get {
                return ResourceManager.GetString("ResponseCode", resourceCulture);
            }
        }
        
        
        ///   查找类似 Pelese Enter JobName Or Args ... 的本地化字符串。
        
        public static string SearchPlaceholder {
            get {
                return ResourceManager.GetString("SearchPlaceholder", resourceCulture);
            }
        }
        
        
        ///   查找类似 use 【name:xxx】 to search jobDetail 的本地化字符串。
        
        public static string SearchPlaceholderUseName {
            get {
                return ResourceManager.GetString("SearchPlaceholderUseName", resourceCulture);
            }
        }
        
        
        ///   查找类似 Specific 的本地化字符串。
        
        public static string Specific {
            get {
                return ResourceManager.GetString("Specific", resourceCulture);
            }
        }
        
        
        ///   查找类似 Specific 的本地化字符串。
        
        public static string SpecificMinute {
            get {
                return ResourceManager.GetString("SpecificMinute", resourceCulture);
            }
        }
        
        
        ///   查找类似 Start 的本地化字符串。
        
        public static string StartBackgroudJobButtonName {
            get {
                return ResourceManager.GetString("StartBackgroudJobButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 Starting on the 的本地化字符串。
        
        public static string StartingAtDay {
            get {
                return ResourceManager.GetString("StartingAtDay", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every 的本地化字符串。
        
        public static string StartingAtDay2 {
            get {
                return ResourceManager.GetString("StartingAtDay2", resourceCulture);
            }
        }
        
        
        ///   查找类似 Starting at hour 的本地化字符串。
        
        public static string StartingAtHour {
            get {
                return ResourceManager.GetString("StartingAtHour", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every 的本地化字符串。
        
        public static string StartingAtHour2 {
            get {
                return ResourceManager.GetString("StartingAtHour2", resourceCulture);
            }
        }
        
        
        ///   查找类似 Starting at minute 的本地化字符串。
        
        public static string StartingAtMinute {
            get {
                return ResourceManager.GetString("StartingAtMinute", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every 的本地化字符串。
        
        public static string StartingAtMinute2 {
            get {
                return ResourceManager.GetString("StartingAtMinute2", resourceCulture);
            }
        }
        
        
        ///   查找类似 Starting on the 的本地化字符串。
        
        public static string StartingAtMonth {
            get {
                return ResourceManager.GetString("StartingAtMonth", resourceCulture);
            }
        }
        
        
        ///   查找类似 Every 的本地化字符串。
        
        public static string StartingAtMontn2 {
            get {
                return ResourceManager.GetString("StartingAtMontn2", resourceCulture);
            }
        }
        
        
        ///   查找类似 StopAgent 的本地化字符串。
        
        public static string StopBackgroudJobButtonName {
            get {
                return ResourceManager.GetString("StopBackgroudJobButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 Submit 的本地化字符串。
        
        public static string SubmitButtonName {
            get {
                return ResourceManager.GetString("SubmitButtonName", resourceCulture);
            }
        }
        
        
        ///   查找类似 Weekday 的本地化字符串。
        
        public static string Weekday {
            get {
                return ResourceManager.GetString("Weekday", resourceCulture);
            }
        }
        
        
        ///   查找类似 Starting on the 的本地化字符串。
        
        public static string WeekDay1 {
            get {
                return ResourceManager.GetString("WeekDay1", resourceCulture);
            }
        }
        
        
        ///   查找类似 st weekday  Specific day of  的本地化字符串。
        
        public static string WeekDay12 {
            get {
                return ResourceManager.GetString("WeekDay12", resourceCulture);
            }
        }
    }
}
