<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>netcoreapp3.1</TargetFramework>
    </PropertyGroup>

    <ItemGroup>
      <Content Include="appsettings.Development.json">
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </Content>
      <Content Include="appsettings.json">
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </Content>
      <Content Include="hangfire\hangfire_global.json">
        <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <None Update="Dockerfile">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
      <None Update="NLog.Config">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Hangfire.Dashboard.BasicAuthorization" Version="1.0.2" />
        <PackageReference Include="Hangfire.InMemory" Version="0.3.4" />
        <PackageReference Include="NLog.Web.AspNetCore" Version="4.9.3" />
    </ItemGroup>
    <ItemGroup>
      <ProjectReference Include="..\..\..\Server\Hangfire.HttpJob\Hangfire.HttpJob.csproj" />
    </ItemGroup>

</Project>
