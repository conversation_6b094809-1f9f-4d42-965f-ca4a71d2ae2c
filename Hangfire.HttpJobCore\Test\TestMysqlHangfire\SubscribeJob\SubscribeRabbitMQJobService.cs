using Hangfire.HttpJob.Server;
using Microsoft.Extensions.Logging;
using RabbitMQ.Client;
using RabbitMQ.Client.Events;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace MysqlHangfire.SubscribeJob
{
    
    
    
    public class SubscribeRabbitMQJobService: ISubscribeJobService
    {
        
        
        
        readonly ILogger<SubscribeRabbitMQJobService> _logger;
        
        
        
        private readonly HttpJobDispatcher _httpJobDispatcher;
        
        
        
        private List<RabbitMQOption> _options = new List<RabbitMQOption>();

        
        
        
        private readonly Random _random = new Random();

        
        
        
        public SubscribeRabbitMQJobService(ILogger<SubscribeRabbitMQJobService> logger)
        {
            _logger = logger;
            _httpJobDispatcher = new HttpJobDispatcher();
        }

        
        
        
        
        
        public Task StartAsync(JObject appSettings)
        {
            _logger.LogInformation("SubscribeJobService:Start:Begin");
            try
            {
                _options = Newtonsoft.Json.JsonConvert.DeserializeObject<List<RabbitMQOption>>(appSettings?["Hangfire"]?["HttpJobOptions"]?["RabbitMQOption"]?.ToString() ?? string.Empty);
                if (_options == null || !_options.Any())
                {
                    _logger.LogInformation("SubscribeJobService:Start:NoOption");
                    return Task.CompletedTask;
                }
                foreach (var item in _options)
                {
                    item.ConnectionFactory = new ConnectionFactory() { HostName = item.HostName, ClientProvidedName = this.GetType().FullName, Port = item.Port, VirtualHost = item.VirtualHost, UserName = item.UserName, Password = item.Password };
                    if (item.Queues == null || !item.Queues.Any()) continue;
                    item.Connection = item.ConnectionFactory.CreateConnection();
                    foreach (var queue in item.Queues)
                    {
                        
                        for (var i = 0; i < queue.NumOfThreads; i++)
                        {
                            queue.Channels.Add(DoWork(item, queue));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"SubscribeJobService:Start:Error:{ex.Message}");
            }

            
            new Timer(CheckConnection, null, TimeSpan.Zero, TimeSpan.FromHours(1));

            _logger.LogInformation("SubscribeJobService:Start:End");
            return Task.CompletedTask;
        }

        
        
        
        
        
        
        
        IModel DoWork(RabbitMQOption item, RabbitMQOptionQueue queue)
        {
            var channel = item.Connection.CreateModel();
            channel.QueueDeclare(queue: queue.Name,
                durable: true,
                exclusive: false,
                autoDelete: false,
                arguments: null);
            channel.BasicQos(prefetchSize: 0, prefetchCount: 1, global: false);
            var consumer = new EventingBasicConsumer(channel);

            consumer.Received += async (model, ea) =>
            {
                try
                {
                    var message = Encoding.UTF8.GetString(ea.Body.ToArray());
                    var jobItem = Newtonsoft.Json.JsonConvert.DeserializeObject<HttpJobItem>(message);
                    if (jobItem != null)
                    {
                        jobItem.DelayFromMinutes = 0;
                        jobItem.EnableRetry = true;
                        jobItem.RetryTimes = 3;
                        jobItem.RetryDelaysInSeconds = "20,30,60";
                        jobItem.Timeout = 180000;

                        
                        if (queue.UrlReplaces.Any() && !string.IsNullOrWhiteSpace(jobItem.Url))
                        {
                            
                            queue.UrlReplaces.ForEach(e => { jobItem.Url = jobItem.Url.Replace(e.OldStr, e.NewStr); });
                        }

                        
                        {
                            if (queue.MatchQueueByUrls.Any())
                            {
                                queue.MatchQueueByUrls.ForEach(e =>
                                {
                                    
                                    if (jobItem.Url.Contains(e.UrlKey) && e.JobQueues.Any())
                                    {
                                        jobItem.QueueName = e.JobQueues[_random.Next(0, e.JobQueues.Count)];
                                    }
                                });
                            }

                            
                            if (string.IsNullOrWhiteSpace(jobItem.QueueName))
                            {
                                jobItem.QueueName = ea.RoutingKey;
                            }
                        }

                        
                        if (string.IsNullOrWhiteSpace(jobItem.JobName))
                        {
                            jobItem.JobName = jobItem.Url;
                        }

                        
                        if (!string.IsNullOrWhiteSpace(jobItem.Data))
                        {
                            var data = Newtonsoft.Json.JsonConvert.DeserializeObject<JToken>(jobItem.Data);
                            if (data != null)
                            {
                                var now = DateTime.Now;
                                if (data.Type == JTokenType.Object &&
                                    string.IsNullOrEmpty(data["LastModifyTime"]?.ToString()))
                                {
                                    data["LastModifyTime"] = now;
                                }
                                else if (data.Type == JTokenType.Array)
                                {
                                    foreach (var jToken in (JArray)data)
                                    {
                                        if (string.IsNullOrEmpty(jToken["LastModifyTime"]
                                                ?.ToString()))
                                        {
                                            jToken["LastModifyTime"] = now;
                                        }
                                    }
                                }

                                jobItem.Data = Newtonsoft.Json.JsonConvert.SerializeObject(data);
                            }
                        }

                        var result = await _httpJobDispatcher.AddBackgroundjob(jobItem);
                        _logger.LogInformation($"SubscribeJobService:{jobItem.JobName}:{result}");
                        if (string.IsNullOrWhiteSpace(result))
                        {
                            channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, true);
                        }
                        else
                        {
                            channel.BasicAck(deliveryTag: ea.DeliveryTag, multiple: false);
                        }
                    }
                    else
                    {
                        throw new NullReferenceException($"jobItem为空:{message}");
                    }
                }
                catch (Exception ex)
                {
                    channel.BasicNack(deliveryTag: ea.DeliveryTag, multiple: false, true);
                    _logger.LogError(ex, $"SubscribeJobService:Error:{ex.Message}");
                }

                Thread.Sleep(1);
            };
            channel.BasicConsume(queue: queue.Name, autoAck: false, consumer: consumer);

            return channel;
        }

        
        
        
        
        private void CheckConnection(object state)
        {
            _logger.LogWarning($"SubscribeJobService:CheckConnection");
            foreach (var item in _options)
            {
                if (item.Connection == null)
                {
                    continue;
                } 
                if (!item.Connection.IsOpen)
                {
                    item.Connection.Close();
                    item.Connection.Dispose();
                    item.Connection = item.ConnectionFactory.CreateConnection();
                }

                if (item.Queues == null || !item.Queues.Any()) continue;
                foreach (var queue in item.Queues)
                {
                    for (var i = 0; i < queue.Channels.Count; i++)
                    {
                        if (!queue.Channels[i].IsOpen || queue.Channels[i].IsClosed)
                        {
                            queue.Channels[i].Close();
                            queue.Channels[i].Dispose();
                            queue.Channels[i] = DoWork(item, queue);
                        }
                    }
                }
            }
        }

        
        
        
        private class RabbitMQOption
        {
            
            
            
            public string HostName { get; set; }
            
            
            
            public int Port { get; set; }
            
            
            
            public string Supplier { get; set; }
            
            
            
            public string VirtualHost { get; set; }
            
            
            
            public string UserName { get; set; }
            
            
            
            public string Password { get; set; }
            
            
            
            public ConnectionFactory ConnectionFactory { get; set; }
            
            
            
            public List<RabbitMQOptionQueue> Queues { get; set; }
            
            
            
            public IConnection Connection { get; set; }
        }

        
        
        
        private class RabbitMQOptionQueue
        {
            
            
            
            public string Name { get; set; }
            
            
            
            public int NumOfThreads { get; set; }

            
            
            
            public List<RabbitMQOptionQueueUrlReplace> UrlReplaces { get; set; } = new List<RabbitMQOptionQueueUrlReplace>();

            
            
            
            public List<RabbitMQOptionQueueMatchQueueByUrl> MatchQueueByUrls { get; set; } = new List<RabbitMQOptionQueueMatchQueueByUrl>();

            
            
            
            public List<IModel> Channels { get; set; } = new List<IModel>();
        }

        
        
        
        private class RabbitMQOptionQueueUrlReplace
        {
            
            
            
            public string OldStr { get; set; }

            
            
            
            public string NewStr { get; set; }
        }

        
        
        
        private class RabbitMQOptionQueueMatchQueueByUrl
        {
            
            
            
            public string UrlKey { get; set; }

            
            
            
            public List<string> JobQueues { get; set; } = new List<string>();
        }
    }
}