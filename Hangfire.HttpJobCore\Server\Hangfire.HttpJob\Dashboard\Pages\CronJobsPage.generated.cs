﻿#pragma warning disable 1591
//------------------------------------------------------------------------------

//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。

//------------------------------------------------------------------------------

namespace Hangfire.HttpJob.Dashboard.Pages
{
    using System;
    
    #line 2 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
    using System.Collections;
    
    #line default
    #line hidden
    
    #line 3 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
    using System.Collections.Generic;
    
    #line default
    #line hidden
    using System.Linq;
    using System.Text;
    
    #line 4 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
    using Hangfire.Dashboard;
    
    #line default
    #line hidden
    
    #line 5 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
    using Hangfire.Dashboard.Pages;
    
    #line default
    #line hidden
    
    #line 6 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
    using Hangfire.Dashboard.Resources;
    
    #line default
    #line hidden
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("RazorGenerator", "2.0.0.0")]
    internal partial class CronJobsPage : RazorPage
    {
#line hidden

        public override void Execute()
        {


WriteLiteral("\r\n");








            
            #line 8 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
  
    Layout = new LayoutPage(Hangfire.HttpJob.Content.resx.Strings.AddCronButtonName);


            
            #line default
            #line hidden
WriteLiteral(@"
<style>
    body {
        width: 50%;
        margin: 0 auto;
        padding-bottom: 20px;
    }

    input[type=number] {
        height: 25px;
        width: 52px;
    }

    .search-field input[type=text] {
        width: 152px !important;
    }
</style>
<div>
    <div style=""text-align: center;"">
        <h3>");


            
            #line 30 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
       Write(Hangfire.HttpJob.Content.resx.Strings.AddCronButtonName);

            
            #line default
            #line hidden
WriteLiteral("</h3>\r\n        <br>\r\n    </div>\r\n    <div>\r\n        <!-- Nav tabs -->\r\n        <u" +
"l class=\"nav nav-tabs\" role=\"tablist\">\r\n            ");



WriteLiteral("\r\n            <li role=\"presentation\" class=\"active\">\r\n                <a href=\"#" +
"minute\" aria-controls=\"minute\" role=\"tab\" data-toggle=\"tab\">");


            
            #line 40 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                                 Write(Hangfire.HttpJob.Content.resx.Strings.Minutes);

            
            #line default
            #line hidden
WriteLiteral("</a>\r\n            </li>\r\n            <li role=\"presentation\">\r\n                <a" +
" href=\"#hour\" aria-controls=\"hour\" role=\"tab\" data-toggle=\"tab\">");


            
            #line 43 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                             Write(Hangfire.HttpJob.Content.resx.Strings.Hours);

            
            #line default
            #line hidden
WriteLiteral("</a>\r\n            </li>\r\n            <li role=\"presentation\">\r\n                <a" +
" href=\"#day\" aria-controls=\"day\" role=\"tab\" data-toggle=\"tab\">");


            
            #line 46 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                           Write(Hangfire.HttpJob.Content.resx.Strings.Days);

            
            #line default
            #line hidden
WriteLiteral("</a>\r\n            </li>\r\n            <li role=\"presentation\">\r\n                <a" +
" href=\"#month\" aria-controls=\"month\" role=\"tab\" data-toggle=\"tab\">");


            
            #line 49 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                               Write(Hangfire.HttpJob.Content.resx.Strings.Months);

            
            #line default
            #line hidden
WriteLiteral("</a>\r\n            </li>\r\n            <li role=\"presentation\">\r\n                <a" +
" href=\"#week\" aria-controls=\"week\" role=\"tab\" data-toggle=\"tab\">");


            
            #line 52 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                             Write(Hangfire.HttpJob.Content.resx.Strings.Weekday);

            
            #line default
            #line hidden
WriteLiteral("</a>\r\n            </li>\r\n            ");



WriteLiteral("\r\n        </ul>\r\n\r\n        <!-- Tab panes -->\r\n        <div class=\"tab-content\">\r" +
"\n\r\n            <!--秒-->\r\n            ");



WriteLiteral(@"

            <!--分钟-->
            <div role=""tabpanel"" class=""tab-pane active"" id=""minute"">
                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""minuteType"" value=""All"" checked=""checked"">
                        ");


            
            #line 109 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.EveryMinute);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n                <div clas" +
"s=\"radio\">\r\n                    <label>\r\n                        <input type=\"ra" +
"dio\" name=\"minuteType\" value=\"Cyclic\">\r\n                        ");


            
            #line 115 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.EveryMinuteBetweenMinute);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"minuteTypeCyclic_1\" value=\"\">\r" +
"\n                        -\r\n                        <input type=\"number\" id=\"min" +
"uteTypeCyclic_2\" value=\"\">\r\n                        ");


            
            #line 119 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Minutes);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n\r\n                <div cl" +
"ass=\"radio\">\r\n                    <label>\r\n                        <input type=\"" +
"radio\" name=\"minuteType\" value=\"Interval\">\r\n                        ");


            
            #line 126 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.StartingAtMinute);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"minuteTypeInterval_1\" value=\"\"" +
">\r\n                        ");


            
            #line 128 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.StartingAtMinute2);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"minuteTypeInterval_2\" value=\"\"" +
">\r\n                        ");


            
            #line 130 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Minutess);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n\r\n                <div cl" +
"ass=\"radio\">\r\n                    <label>\r\n                        <input type=\"" +
"radio\" name=\"minuteType\" value=\"Assigned\">\r\n                        ");


            
            #line 137 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.SpecificMinute);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n              \r\n         " +
"       <div style=\"margin-left: 20px;\">\r\n                    <select id=\"minuteT" +
"ypeAssigned_1\" data-placeholder=\"");


            
            #line 142 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                   Write(Hangfire.HttpJob.Content.resx.Strings.Choose);

            
            #line default
            #line hidden
WriteLiteral(@"""
                            style=""width:350px;"" multiple></select>
                </div>
                
                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""minuteType"" value=""NotAssigned"">
                        ");


            
            #line 149 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.NoSpecific);

            
            #line default
            #line hidden
WriteLiteral(@"
                    </label>
                </div>
            </div>

            <!--小时-->
            <div role=""tabpanel"" class=""tab-pane"" id=""hour"">

                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""hourType"" value=""All"" checked=""checked"">
                        ");


            
            #line 160 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.EveryHour);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n                <div clas" +
"s=\"radio\">\r\n                    <label>\r\n                        <input type=\"ra" +
"dio\" name=\"hourType\" value=\"Cyclic\">\r\n                        ");


            
            #line 166 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.EveryHourBetweenHour);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"hourTypeCyclic_1\" value=\"\">\r\n " +
"                       -\r\n                        <input type=\"number\" id=\"hourT" +
"ypeCyclic_2\" value=\"\">\r\n                        ");


            
            #line 170 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Hours);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n\r\n                <div cl" +
"ass=\"radio\">\r\n                    <label>\r\n                        <input type=\"" +
"radio\" name=\"hourType\" value=\"Interval\">\r\n                        ");


            
            #line 177 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.StartingAtHour);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"hourTypeInterval_1\" value=\"\">\r" +
"\n                        ");


            
            #line 179 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.StartingAtHour2);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"hourTypeInterval_2\" value=\"\">\r" +
"\n                        ");


            
            #line 181 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Hourss);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n\r\n                <div cl" +
"ass=\"radio\">\r\n                    <label>\r\n                        <input type=\"" +
"radio\" name=\"hourType\" value=\"Assigned\">\r\n                        ");


            
            #line 188 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Specific);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n\r\n                <div st" +
"yle=\"margin-left: 20px;\">\r\n                    <select id=\"hourTypeAssigned_1\" d" +
"ata-placeholder=\"");


            
            #line 193 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                 Write(Hangfire.HttpJob.Content.resx.Strings.Choose);

            
            #line default
            #line hidden
WriteLiteral(@"""
                            style=""width:350px;"" multiple></select>
                </div>
                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""hourType"" value=""NotAssigned"">
                        ");


            
            #line 199 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.NoSpecific);

            
            #line default
            #line hidden
WriteLiteral(@"
                    </label>
                </div>
            </div>


            <!--日-->
            <div role=""tabpanel"" class=""tab-pane"" id=""day"">

                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""dayType"" value=""All"" checked=""checked"">
                        ");


            
            #line 211 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.EveryDay);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n                <div clas" +
"s=\"radio\">\r\n                    <label>\r\n                        <input type=\"ra" +
"dio\" name=\"dayType\" value=\"Cyclic\">\r\n                        ");


            
            #line 217 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.EveryDayBetweenDay);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"dayTypeCyclic_1\" value=\"\">\r\n  " +
"                      -\r\n                        <input type=\"number\" id=\"dayTyp" +
"eCyclic_2\" value=\"\">\r\n                        ");


            
            #line 221 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Days);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n\r\n                <div cl" +
"ass=\"radio\">\r\n                    <label>\r\n                        <input type=\"" +
"radio\" name=\"dayType\" value=\"Interval\">\r\n                        ");


            
            #line 228 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.StartingAtDay);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"dayTypeInterval_1\" value=\"\">\r\n" +
"                        ");


            
            #line 230 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.StartingAtDay2);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"dayTypeInterval_2\" value=\"\">\r\n" +
"                        ");


            
            #line 232 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Dayss);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n\r\n                <div cl" +
"ass=\"radio\">\r\n                    <label>\r\n                        <input type=\"" +
"radio\" name=\"dayType\" value=\"Assigned\">\r\n                        ");


            
            #line 239 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Specific);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n                <div styl" +
"e=\"margin-left: 20px;\">\r\n                    <select id=\"dayTypeAssigned_1\" data" +
"-placeholder=\"");


            
            #line 243 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                Write(Hangfire.HttpJob.Content.resx.Strings.Choose);

            
            #line default
            #line hidden
WriteLiteral(@"""
                            style=""width:350px;"" multiple></select>
                </div>

                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""dayType"" value=""RecentDays"">
                        ");


            
            #line 250 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.DayA);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"dayTypeRecentDays_1\" value=\"\">" +
"\r\n                        ");


            
            #line 252 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.DayB);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n                <div clas" +
"s=\"radio\">\r\n                    <label>\r\n                        <input type=\"ra" +
"dio\" name=\"dayType\" value=\"LastDayOfMonth\">\r\n                        ");


            
            #line 258 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.LastDayOfMonth);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n                <div clas" +
"s=\"radio\">\r\n                    <label>\r\n                        <input type=\"ra" +
"dio\" name=\"dayType\" value=\"LastDayOfMonthRecentDays\">\r\n                        ");


            
            #line 264 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.LastWeedDayOfMonth);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n\r\n                <div cl" +
"ass=\"radio\">\r\n                    <label>\r\n                        <input type=\"" +
"radio\" name=\"dayType\" value=\"NotAssigned\">\r\n                        ");


            
            #line 271 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.NoSpecific);

            
            #line default
            #line hidden
WriteLiteral(@"
                    </label>
                </div>
            </div>


            <!--月-->
            <div role=""tabpanel"" class=""tab-pane"" id=""month"">
                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""monthType"" value=""All"" checked=""checked"">
                        ");


            
            #line 282 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.EveryMonth);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n                <div clas" +
"s=\"radio\">\r\n                    <label>\r\n                        <input type=\"ra" +
"dio\" name=\"monthType\" value=\"Cyclic\">\r\n                        ");


            
            #line 288 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.EveryMonthBetweenMonth);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"monthTypeCyclic_1\" value=\"\">\r\n" +
"                        -\r\n                        <input type=\"number\" id=\"mont" +
"hTypeCyclic_2\" value=\"\">\r\n                        ");


            
            #line 292 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Months);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n\r\n                <div cl" +
"ass=\"radio\">\r\n                    <label>\r\n                        <input type=\"" +
"radio\" name=\"monthType\" value=\"Interval\">\r\n                        ");


            
            #line 299 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.StartingAtMonth);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"monthTypeInterval_1\" value=\"\">" +
"\r\n                        ");


            
            #line 301 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.StartingAtMontn2);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"monthTypeInterval_2\" value=\"\">" +
"\r\n                        ");


            
            #line 303 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Monthss);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n\r\n                <div cl" +
"ass=\"radio\">\r\n                    <label>\r\n                        <input type=\"" +
"radio\" name=\"monthType\" value=\"Assigned\">\r\n                        ");


            
            #line 310 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Specific);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n                <div styl" +
"e=\"margin-left: 20px;\">\r\n                    <select id=\"monthTypeAssigned_1\" da" +
"ta-placeholder=\"");


            
            #line 314 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                  Write(Hangfire.HttpJob.Content.resx.Strings.Choose);

            
            #line default
            #line hidden
WriteLiteral(@"""
                            style=""width:350px;"" multiple></select>
                </div>

                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""monthType"" value=""NotAssigned"">
                        ");


            
            #line 321 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.NoSpecific);

            
            #line default
            #line hidden
WriteLiteral(@"
                    </label>
                </div>
            </div>


            <!--周-->
            <div role=""tabpanel"" class=""tab-pane"" id=""week"">
                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""weekType"" value=""All"" checked=""checked"">
                        ");


            
            #line 332 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.EveryWeekday);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n                <div clas" +
"s=\"radio\">\r\n                    <label>\r\n                        <input type=\"ra" +
"dio\" name=\"weekType\" value=\"Cyclic\">\r\n                        ");


            
            #line 338 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.EveryWeekdayBetweenWeekday);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"weekTypeCyclic_1\" value=\"\">\r\n " +
"                       -\r\n                        <input type=\"number\" id=\"weekT" +
"ypeCyclic_2\" value=\"\">\r\n                        ");


            
            #line 342 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Weekday);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n\r\n                <div cl" +
"ass=\"radio\">\r\n                    <label>\r\n                        <input type=\"" +
"radio\" name=\"weekType\" value=\"WeeksOfWeek\">\r\n                        ");


            
            #line 349 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.WeekDay1);

            
            #line default
            #line hidden
WriteLiteral("\r\n                        <input type=\"number\" id=\"weekTypeWeeksOfWeek_1\" value=\"" +
"\">\r\n                        ");


            
            #line 351 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.WeekDay12);

            
            #line default
            #line hidden
WriteLiteral(@"
                        <input type=""number"" id=""weekTypeWeeksOfWeek_2"" value="""">

                    </label>
                </div>

                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""weekType"" value=""Assigned"">
                        ");


            
            #line 360 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.Specific);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n                <div styl" +
"e=\"margin-left: 20px;\">\r\n                    <select id=\"weekTypeAssigned_1\" dat" +
"a-placeholder=\"");


            
            #line 364 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                 Write(Hangfire.HttpJob.Content.resx.Strings.Choose);

            
            #line default
            #line hidden
WriteLiteral(@"""
                            style=""width:350px;"" multiple></select>
                </div>

                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""weekType"" value=""LastWeekOfMonth"">
                        ");


            
            #line 371 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.LastWeekdayOfMonth);

            
            #line default
            #line hidden
WriteLiteral(@"
                        <input type=""number"" id=""weekTypeLastWeekOfMonth_1"" value=""1"">

                    </label>
                </div>


                <div class=""radio"">
                    <label>
                        <input type=""radio"" name=""weekType"" value=""NotAssigned"">
                        ");


            
            #line 381 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                   Write(Hangfire.HttpJob.Content.resx.Strings.NoSpecific);

            
            #line default
            #line hidden
WriteLiteral("\r\n                    </label>\r\n                </div>\r\n            </div>\r\n\r\n\r\n " +
"           <!--年-->\r\n            ");



WriteLiteral("\r\n\r\n        </div>\r\n    </div>\r\n    <hr>\r\n\r\n    <div class=\"panel panel-info\">\r\n " +
"       <div class=\"panel-heading\">\r\n            <h3 class=\"panel-title\">");


            
            #line 431 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                               Write(Hangfire.HttpJob.Content.resx.Strings.CronResult);

            
            #line default
            #line hidden
WriteLiteral(@"</h3>
        </div>

        <div class=""panel-body"">

            <form class=""form-inline"">
                <!--<div class=""form-group"">
                    <label class=""sr-only"">结果</label>
                    <p class=""form-control-static"">结果</p>
                </div>-->
                <div class=""form-group"">
                    <label for=""result"" class=""sr-only"">Password</label>
                    <input type=""text"" class=""form-control"" style=""width: 500px;"" id=""result"" placeholder=""");


            
            #line 443 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                                                      Write(Hangfire.HttpJob.Content.resx.Strings.CronResult);

            
            #line default
            #line hidden
WriteLiteral("\">\r\n                </div>\r\n                <button type=\"button\" id=\"analysis\" c" +
"lass=\"btn btn-default\">");


            
            #line 445 "..\..\Dashboard\Pages\CronJobsPage.cshtml"
                                                                       Write(Hangfire.HttpJob.Content.resx.Strings.DESCRIBEEXPRESSION);

            
            #line default
            #line hidden
WriteLiteral("</button>\r\n            </form>\r\n        </div>\r\n    </div>\r\n</div>");


        }
    }
}
#pragma warning restore 1591
