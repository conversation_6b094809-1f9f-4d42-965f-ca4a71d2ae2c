{"runtimeTarget": {"name": ".NETStandard,Version=v2.0/", "signature": ""}, "compilationOptions": {}, "targets": {".NETStandard,Version=v2.0": {}, ".NETStandard,Version=v2.0/": {"Hangfire.MySql/1.0.0": {"dependencies": {"Dapper": "1.50.5", "Hangfire.Core": "1.6.21", "Microsoft.CSharp": "4.4.1", "MySqlConnector": "2.3.0-beta.3", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "11.0.2"}, "runtime": {"Hangfire.MySql.dll": {}}}, "Dapper/1.50.5": {"dependencies": {"System.Data.SqlClient": "4.4.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.TypeExtensions": "4.4.0"}, "runtime": {"lib/netstandard2.0/Dapper.dll": {"assemblyVersion": "1.50.5.0", "fileVersion": "1.50.5.0"}}}, "Hangfire.Core/1.6.21": {"dependencies": {"Microsoft.NETCore.Portable.Compatibility": "1.0.1", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "11.0.2", "System.Threading.Thread": "4.0.0"}, "runtime": {"lib/netstandard1.3/Hangfire.Core.dll": {"assemblyVersion": "1.6.21.0", "fileVersion": "1.6.21.0"}}, "resources": {"lib/netstandard1.3/es/Hangfire.Core.resources.dll": {"locale": "es"}, "lib/netstandard1.3/zh-TW/Hangfire.Core.resources.dll": {"locale": "zh-TW"}, "lib/netstandard1.3/zh/Hangfire.Core.resources.dll": {"locale": "zh"}}}, "Microsoft.CSharp/4.4.1": {"runtime": {"lib/netstandard2.0/Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25921.2"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.NETCore.Jit/1.0.2": {}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Portable.Compatibility/1.0.1": {"dependencies": {"Microsoft.NETCore.Runtime.CoreCLR": "1.0.2"}, "runtime": {"lib/netstandard1.0/System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}, "lib/netstandard1.0/System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}, "lib/netstandard1.0/System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}, "lib/netstandard1.0/System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}, "lib/netstandard1.0/System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}, "lib/netstandard1.0/System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}, "lib/netstandard1.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}, "lib/netstandard1.0/System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}, "lib/netstandard1.0/System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}, "lib/netstandard1.0/System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}, "lib/netstandard1.0/System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}, "lib/netstandard1.0/System.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}}}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"dependencies": {"Microsoft.NETCore.Jit": "1.0.2", "Microsoft.NETCore.Windows.ApiSets": "1.0.1"}}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {}, "Microsoft.Win32.Registry/4.4.0": {"dependencies": {"System.Security.AccessControl": "4.4.0", "System.Security.Principal.Windows": "4.4.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "MySqlConnector/2.3.0-beta.3": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "7.0.0", "System.Buffers": "4.5.1", "System.Diagnostics.DiagnosticSource": "7.0.0", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/MySqlConnector.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "Newtonsoft.Json/11.0.2": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "11.0.2.21924"}}}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "System.Buffers/4.5.1": {"runtime": {"lib/netstandard2.0/System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.28619.1"}}}, "System.Data.SqlClient/4.4.0": {"dependencies": {"Microsoft.Win32.Registry": "4.4.0", "System.Diagnostics.DiagnosticSource": "7.0.0", "System.Security.Principal.Windows": "4.4.0", "System.Text.Encoding.CodePages": "4.4.0", "runtime.native.System.Data.SqlClient.sni": "4.4.0"}, "runtime": {"lib/netstandard2.0/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Diagnostics.DiagnosticSource/7.0.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Memory/4.5.5": {"dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.31308.1"}}}, "System.Numerics.Vectors/4.4.0": {"runtime": {"lib/netstandard2.0/System.Numerics.Vectors.dll": {"assemblyVersion": "4.1.3.0", "fileVersion": "4.6.25519.3"}}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "4.0.2.0", "fileVersion": "4.6.24705.1"}}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.4.0": {"runtime": {"lib/netstandard2.0/System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Security.AccessControl/4.4.0": {"dependencies": {"System.Security.Principal.Windows": "4.4.0"}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Security.Principal.Windows/4.4.0": {"runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/4.4.0": {"runtime": {"lib/netstandard2.0/System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.28619.1"}}}, "System.Threading.Thread/4.0.0": {"dependencies": {"System.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.3/System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.24212.1"}}}}}, "libraries": {"Hangfire.MySql/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Dapper/1.50.5": {"type": "package", "serviceable": true, "sha512": "sha512-1vPpX7WQmQCIb7rwlGOUoVs/yWZhVKvdhuG7WrJV+V+qsP8btnrrCqVWHENAlJxBAnUw5rhWfmuba9/Egei9MA==", "path": "dapper/1.50.5", "hashPath": "dapper.1.50.5.nupkg.sha512"}, "Hangfire.Core/1.6.21": {"type": "package", "serviceable": true, "sha512": "sha512-/TXpaj+ccCpQs06PmOJfV13St+UksLpQuvis6OsMd1jee5JhPOVmaERG2CcG/ClTMjjNxf6msfapu4sMCaVVZQ==", "path": "hangfire.core/1.6.21", "hashPath": "hangfire.core.1.6.21.nupkg.sha512"}, "Microsoft.CSharp/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-A5hI3gk6WpcBI0QGZY6/d5CCaYUxJgi7iENn1uYEng+Olo8RfI5ReGVkjXjeu3VR3srLvVYREATXa2M0X7FYJA==", "path": "microsoft.csharp/4.4.1", "hashPath": "microsoft.csharp.4.4.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.NETCore.Jit/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ok2vWofa6X8WD9vc4pfLHwvJz1/B6t3gOAoZcjrjrQf7lQOlNIuZIZtLn3wnWX28DuQGpPJkRlBxFj7Z5txNqw==", "path": "microsoft.netcore.jit/1.0.2", "hashPath": "microsoft.netcore.jit.1.0.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Portable.Compatibility/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vd+lvLcGwvkedxtKn0U8s9uR4p0Lm+0U2QvDsLaw7g4S1W4KfPDbaW+ROhhLCSOx/gMYC72/b+z+o4fqS/oxVg==", "path": "microsoft.netcore.portable.compatibility/1.0.1", "hashPath": "microsoft.netcore.portable.compatibility.1.0.1.nupkg.sha512"}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-A0x1xtTjYJWZr2DRzgfCOXgB0JkQg8twnmtTJ79wFje+IihlLbXtx6Z2AxyVokBM5ruwTedR6YdCmHk39QJdtQ==", "path": "microsoft.netcore.runtime.coreclr/1.0.2", "hashPath": "microsoft.netcore.runtime.coreclr.1.0.2.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-SaToCvvsGMxTgtLv/BrFQ5IFMPRE1zpWbnqbpwykJa8W5XiX82CXI6K2o7yf5xS7EP6t/JzFLV0SIDuWpvBZVw==", "path": "microsoft.netcore.windows.apisets/1.0.1", "hashPath": "microsoft.netcore.windows.apisets.1.0.1.nupkg.sha512"}, "Microsoft.Win32.Registry/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-dA36TlNVn/XfrZtmf0fiI/z1nd3Wfp2QVzTdj26pqgP9LFWq0i1hYEUAW50xUjGFYn1+/cP3KGuxT2Yn1OUNBQ==", "path": "microsoft.win32.registry/4.4.0", "hashPath": "microsoft.win32.registry.4.4.0.nupkg.sha512"}, "MySqlConnector/2.3.0-beta.3": {"type": "package", "serviceable": true, "sha512": "sha512-lI8KNSKd/BWPX71NGgtDpXDMvDKENrLOfQBiiLho21m/50V66sC3eh2VfUqJ83vvGEygktprT+8RFLrH/eVmRw==", "path": "mysqlconnector/2.3.0-beta.3", "hashPath": "mysqlconnector.2.3.0-beta.3.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Newtonsoft.Json/11.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-IvJe1pj7JHEsP8B8J8DwlMEx8UInrs/x+9oVY+oCD13jpLu4JbJU2WCIsMRn5C4yW9+DgkaO8uiVE5VHKjpmdQ==", "path": "newtonsoft.json/11.0.2", "hashPath": "newtonsoft.json.11.0.2.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-A8v6PGmk+UGbfWo5Ixup0lPM4swuSwOiayJExZwKIOjTlFFQIsu3QnDXECosBEyrWSPryxBVrdqtJyhK3BaupQ==", "path": "runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.Data.SqlClient/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-fxb9ghn1k1Ua7FFdlvtiBOD4/PsQvD/fk2KnhS+FK7VC6OggEx6P+lP1P0+KMb5V2dqS1+FbR7HCenoqzJMNIA==", "path": "system.data.sqlclient/4.4.0", "hashPath": "system.data.sqlclient.4.4.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9W0ewWDuAyDqS2PigdTxk6jDKonfgscY/hP8hm7VpxYhNHZHKvZTdRckberlFk3VnCmr3xBUyMBut12Q+T2aOw==", "path": "system.diagnostics.diagnosticsource/7.0.0", "hashPath": "system.diagnostics.diagnosticsource.7.0.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Numerics.Vectors/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-UiLzLW+Lw6HLed1Hcg+8jSRttrbuXv7DANVj0DkL9g6EnnzbL75EB7EWsw5uRbhxd/4YdG8li5XizGWepmG3PQ==", "path": "system.numerics.vectors/4.4.0", "hashPath": "system.numerics.vectors.4.4.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkmh/ySlwnXJp/1qYP9uyKkCK1CXR/REFzl7abHcArxBcV91mY2CgrrzSRA5Z/X4MevJWwXsklGRdR3A7K9zbg==", "path": "system.reflection.typeextensions/4.4.0", "hashPath": "system.reflection.typeextensions.4.4.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2NRFPX/V81ucKQmqNgGBZrKGH/5ejsvivSGMRum0SMgPnJxwhuNkzVS1+7gC3R2X0f57CtwrPrXPPSe6nOp82g==", "path": "system.security.accesscontrol/4.4.0", "hashPath": "system.security.accesscontrol.4.4.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-pP+AOzt1o3jESOuLmf52YQTF7H3Ng9hTnrOESQiqsnl2IbBh1HInsAMHYtoh75iUYV0OIkHmjvveraYB6zM97w==", "path": "system.security.principal.windows/4.4.0", "hashPath": "system.security.principal.windows.4.4.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-6J<PERSON><PERSON>ZdaceBiLKLkYt8zJcp4xTJd1uYyXXEkPw6mnlUIjh1gZPIVKPtRXPmY5kLf6DwZmf5YLwR3QUrRonl7l0A==", "path": "system.text.encoding.codepages/4.4.0", "hashPath": "system.text.encoding.codepages.4.4.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.Threading.Thread/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gIdJqDXlOr5W9zeqFErLw3dsOsiShSCYtF9SEHitACycmvNvY8odf9kiKvp6V7aibc8C4HzzNBkWXjyfn7plbQ==", "path": "system.threading.thread/4.0.0", "hashPath": "system.threading.thread.4.0.0.nupkg.sha512"}}}